#!/usr/bin/env python3
"""
Test script for the memory integration implementation.

This script tests the basic functionality of the mem0 memory system
with local Qdrant and sentence transformers.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.memory import memory_service


async def test_memory_integration():
    """Test the memory integration functionality."""
    print("🧠 Testing Memory Integration")
    print("=" * 50)
    
    # Test data
    user_id = "test_user_123"
    agent_id = "test_agent_456"
    session_id = "test_session_789"
    
    try:
        # Test 1: Store user query
        print("📝 Test 1: Storing user query...")
        user_message = "My age is 24 and I love playing basketball"
        success = await memory_service.store_user_query(
            user_message=user_message,
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            metadata={"test": True}
        )
        print(f"✅ User query stored: {success}")
        
        # Test 2: Store agent response
        print("\n🤖 Test 2: Storing agent response...")
        agent_response = "That's great! Basketball is an excellent sport for staying fit at 24."
        success = await memory_service.store_agent_response(
            agent_response=agent_response,
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            metadata={"test": True}
        )
        print(f"✅ Agent response stored: {success}")
        
        # Test 3: Retrieve relevant context
        print("\n🔍 Test 3: Retrieving relevant context...")
        query = "What sports do I like?"
        context = await memory_service.get_relevant_context(
            query=query,
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            k=3
        )
        print(f"✅ Retrieved context: {context}")
        
        # Test 4: Store conversation exchange
        print("\n💬 Test 4: Storing conversation exchange...")
        user_msg = "I also enjoy reading science fiction books"
        agent_resp = "Science fiction is fascinating! Do you have a favorite author?"
        success = await memory_service.store_conversation_exchange(
            user_message=user_msg,
            agent_response=agent_resp,
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            metadata={"test": True}
        )
        print(f"✅ Conversation exchange stored: {success}")
        
        # Test 5: Retrieve context for new query
        print("\n📚 Test 5: Retrieving context for books query...")
        query = "What are my hobbies?"
        context = await memory_service.get_relevant_context(
            query=query,
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            k=5
        )
        print(f"✅ Retrieved context for hobbies: {context}")
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


async def test_memory_filtering():
    """Test memory filtering by user, agent, and conversation."""
    print("\n🔧 Testing Memory Filtering")
    print("=" * 50)
    
    try:
        # Store memories for different users/agents
        await memory_service.store_user_query(
            user_message="I am user 1 and I like tennis",
            user_id="user_1",
            agent_id="agent_1",
            session_id="session_1"
        )
        
        await memory_service.store_user_query(
            user_message="I am user 2 and I like football",
            user_id="user_2",
            agent_id="agent_1",
            session_id="session_2"
        )
        
        # Test filtering by user
        context_user1 = await memory_service.get_relevant_context(
            query="What sport do I like?",
            user_id="user_1",
            agent_id="agent_1",
            session_id="session_1"
        )
        
        context_user2 = await memory_service.get_relevant_context(
            query="What sport do I like?",
            user_id="user_2",
            agent_id="agent_1",
            session_id="session_2"
        )
        
        print(f"✅ User 1 context: {context_user1}")
        print(f"✅ User 2 context: {context_user2}")
        
        # Verify filtering works
        if "tennis" in context_user1 and "football" in context_user2:
            print("✅ Memory filtering works correctly!")
        else:
            print("❌ Memory filtering may not be working correctly")
            
    except Exception as e:
        print(f"❌ Error during filtering test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


def check_prerequisites():
    """Check if required services are available."""
    print("🔍 Checking Prerequisites")
    print("=" * 50)
    
    # Check if Qdrant is running (optional for this test)
    try:
        import requests
        response = requests.get("http://localhost:6333/health", timeout=5)
        if response.status_code == 200:
            print("✅ Qdrant is running on localhost:6333")
        else:
            print("⚠️  Qdrant health check failed")
    except Exception:
        print("⚠️  Qdrant is not running on localhost:6333")
        print("   You can start it with: docker-compose up qdrant")
    
    # Check environment variables
    openai_key = os.getenv("OPENAI_API_KEY")
    if openai_key:
        print("✅ OPENAI_API_KEY is set")
    else:
        print("⚠️  OPENAI_API_KEY is not set (required for embeddings)")
    
    print()


async def main():
    """Main test function."""
    print("🚀 Memory Integration Test Suite")
    print("=" * 50)
    
    check_prerequisites()
    
    # Run tests
    test1_success = await test_memory_integration()
    test2_success = await test_memory_filtering()
    
    print("\n📊 Test Results")
    print("=" * 50)
    print(f"Memory Integration Test: {'✅ PASSED' if test1_success else '❌ FAILED'}")
    print(f"Memory Filtering Test: {'✅ PASSED' if test2_success else '❌ FAILED'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed! Memory integration is working correctly.")
        print("\n📋 Next Steps:")
        print("1. Start Qdrant: docker-compose up qdrant")
        print("2. Set OPENAI_API_KEY environment variable")
        print("3. Test with your agent platform")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
