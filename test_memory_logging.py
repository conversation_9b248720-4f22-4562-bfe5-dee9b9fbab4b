#!/usr/bin/env python3
"""
Test script to verify memory logging is working correctly.

This script tests the memory system with detailed logging to show
what's being stored and retrieved.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.memory import memory_service

# Configure logging to show all memory operations
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Set specific loggers to INFO level
logging.getLogger("app.memory").setLevel(logging.INFO)
logging.getLogger("app.autogen_service").setLevel(logging.INFO)
logging.getLogger("app.kafka_client").setLevel(logging.INFO)


async def test_memory_with_logging():
    """Test memory operations with detailed logging."""
    print("🧪 Testing Memory System with Detailed Logging")
    print("=" * 60)
    
    # Test data
    user_id = "test_user_456"
    agent_id = "test_agent_789"
    session_id = "test_session_123"
    
    try:
        print("\n🔄 Step 1: Storing user query...")
        user_message = "I am 25 years old and I work as a software engineer at Google"
        await memory_service.store_user_query(
            user_message=user_message,
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            metadata={"test": "logging_test", "step": 1}
        )
        
        print("\n🔄 Step 2: Storing agent response...")
        agent_response = "That's great! Working as a software engineer at Google at 25 is quite an achievement. How long have you been working there?"
        await memory_service.store_agent_response(
            agent_response=agent_response,
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            metadata={"test": "logging_test", "step": 2}
        )
        
        print("\n🔄 Step 3: Retrieving context for related query...")
        query = "What do you know about my job?"
        context = await memory_service.get_relevant_context(
            query=query,
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            k=5
        )
        
        print("\n🔄 Step 4: Storing another user query...")
        user_message2 = "I also love playing tennis on weekends"
        await memory_service.store_user_query(
            user_message=user_message2,
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            metadata={"test": "logging_test", "step": 4}
        )
        
        print("\n🔄 Step 5: Retrieving context for hobby query...")
        query2 = "What are my hobbies and interests?"
        context2 = await memory_service.get_relevant_context(
            query=query2,
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            k=5
        )
        
        print("\n🔄 Step 6: Testing conversation exchange...")
        await memory_service.store_conversation_exchange(
            user_message="I'm thinking of switching to a different tech company",
            agent_response="That's an interesting decision! What's motivating you to consider a change from Google?",
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            metadata={"test": "logging_test", "step": 6}
        )
        
        print("\n✅ All memory operations completed successfully!")
        print("📋 Check the logs above to see detailed memory operations")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


async def main():
    """Main test function."""
    print("🚀 Memory Logging Test Suite")
    print("=" * 60)
    
    # Check prerequisites
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key:
        print("⚠️  OPENAI_API_KEY is not set")
        print("   Please set your OpenAI API key to run this test")
        return
    
    print("✅ OPENAI_API_KEY is set")
    
    # Run test
    success = await test_memory_with_logging()
    
    print("\n📊 Test Results")
    print("=" * 60)
    if success:
        print("🎉 Memory logging test completed successfully!")
        print("\n📝 What to look for in the logs:")
        print("   🧠 Memory service initialization")
        print("   📝 User query storage with metadata")
        print("   🤖 Agent response storage with metadata")
        print("   🔍 Memory search operations")
        print("   📋 Retrieved memories with scores")
        print("   🎯 Context generation")
        print("   💬 Conversation exchange completion")
    else:
        print("❌ Memory logging test failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
