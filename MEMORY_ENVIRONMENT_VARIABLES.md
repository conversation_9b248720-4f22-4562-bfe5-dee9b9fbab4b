# Memory System Environment Variables

## 📋 **Required Environment Variables**

Add these environment variables to your `.env` file to configure the memory system:

### **🗄️ Qdrant Configuration**
```bash
# Qdrant Vector Database Settings
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=agent_memory
QDRANT_VECTOR_SIZE=384
QDRANT_DISTANCE=Cosine
QDRANT_API_KEY=  # Optional, leave empty for local Qdrant
QDRANT_TIMEOUT=60
```

### **🧠 Mem0 Memory Configuration**
```bash
# Mem0 Memory System Settings
MEM0_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
MEM0_VECTOR_STORE_TYPE=qdrant
MEM0_LLM_PROVIDER=openai
MEM0_LLM_MODEL=gpt-4o-mini
MEM0_MEMORY_DECAY_RATE=0.01
MEM0_MAX_MEMORIES=10000
MEM0_MIN_RELEVANCE_SCORE=0.2
```

### **🔑 Required API Keys**
```bash
# OpenAI API Key (for memory processing LLM)
OPENAI_API_KEY=your_openai_api_key_here
```

## 🎯 **Configuration Options**

### **📏 Vector Dimensions**
The `QDRANT_VECTOR_SIZE` must match your embedding model:

| Model | Dimensions | Environment Variable |
|-------|------------|---------------------|
| `all-MiniLM-L6-v2` | 384 | `QDRANT_VECTOR_SIZE=384` |
| `all-mpnet-base-v2` | 768 | `QDRANT_VECTOR_SIZE=768` |
| `all-MiniLM-L12-v2` | 384 | `QDRANT_VECTOR_SIZE=384` |

### **🤖 Embedding Models**
Choose from these sentence transformer models:

```bash
# Fast and efficient (recommended)
MEM0_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# Higher quality but slower
MEM0_EMBEDDING_MODEL=sentence-transformers/all-mpnet-base-v2

# Balanced option
MEM0_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L12-v2
```

### **🧠 LLM Models**
Configure the LLM used for memory processing:

```bash
# OpenAI Models
MEM0_LLM_MODEL=gpt-4o-mini        # Fast and cost-effective (recommended)
MEM0_LLM_MODEL=gpt-4o             # Higher quality
MEM0_LLM_MODEL=gpt-3.5-turbo      # Budget option

# Other providers (if supported)
MEM0_LLM_PROVIDER=anthropic
MEM0_LLM_MODEL=claude-3-haiku
```

## 🔧 **Advanced Configuration**

### **📊 Memory Management**
```bash
# Memory decay rate (how fast old memories fade)
MEM0_MEMORY_DECAY_RATE=0.01       # Default: 1% decay

# Maximum number of memories to store
MEM0_MAX_MEMORIES=10000           # Default: 10,000 memories

# Memory relevance threshold (configurable via env var)
MEM0_MIN_RELEVANCE_SCORE=0.2      # Default: 0.2 (lower = more memories included)
```

### **🗄️ Qdrant Advanced Settings**
```bash
# For cloud Qdrant instances
QDRANT_HOST=your-cluster.qdrant.io
QDRANT_PORT=6333
QDRANT_API_KEY=your_qdrant_api_key

# Distance metrics
QDRANT_DISTANCE=Cosine            # Recommended for sentence transformers
QDRANT_DISTANCE=Dot               # Alternative option
QDRANT_DISTANCE=Euclid            # Not recommended for embeddings
```

## 🚀 **Production Recommendations**

### **🏭 Production Environment**
```bash
# Production-optimized settings
QDRANT_VECTOR_SIZE=384
MEM0_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
MEM0_LLM_MODEL=gpt-4o-mini
MEM0_MAX_MEMORIES=50000
MEM0_MEMORY_DECAY_RATE=0.005
QDRANT_TIMEOUT=120
```

### **💰 Cost-Optimized Settings**
```bash
# Minimize OpenAI API costs
MEM0_LLM_MODEL=gpt-4o-mini        # Cheapest OpenAI model
MEM0_MAX_MEMORIES=5000            # Limit memory storage
MEM0_MEMORY_DECAY_RATE=0.02       # Faster decay = fewer memories
```

### **⚡ Performance-Optimized Settings**
```bash
# Maximize speed
MEM0_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2  # Fastest model
QDRANT_VECTOR_SIZE=384            # Smaller vectors = faster search
QDRANT_TIMEOUT=30                 # Shorter timeout
```

## 🧪 **Development vs Production**

### **🔧 Development (.env.development)**
```bash
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=agent_memory_dev
MEM0_MAX_MEMORIES=1000
MEM0_LLM_MODEL=gpt-4o-mini
```

### **🏭 Production (.env.production)**
```bash
QDRANT_HOST=your-production-qdrant-host
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=agent_memory_prod
QDRANT_API_KEY=your_production_api_key
MEM0_MAX_MEMORIES=50000
MEM0_LLM_MODEL=gpt-4o-mini
```

## 🔄 **Migration Guide**

### **Changing Vector Dimensions**
If you change `QDRANT_VECTOR_SIZE` or `MEM0_EMBEDDING_MODEL`:

1. **Reset Qdrant Collection**:
   ```bash
   poetry run python scripts/reset_qdrant_collection.py
   ```

2. **Update Environment Variables**:
   ```bash
   QDRANT_VECTOR_SIZE=768  # New dimension
   MEM0_EMBEDDING_MODEL=sentence-transformers/all-mpnet-base-v2  # New model
   ```

3. **Restart Application**:
   The new model will be preloaded automatically.

## ✅ **Validation**

### **Check Configuration**
```bash
# Test that all environment variables are loaded correctly
poetry run python -c "
from app.shared.config.base import get_settings
settings = get_settings()
print(f'Qdrant: {settings.qdrant.host}:{settings.qdrant.port}')
print(f'Collection: {settings.qdrant.collection_name}')
print(f'Vector Size: {settings.qdrant.vector_size}')
print(f'Embedding Model: {settings.mem0.embedding_model}')
print(f'LLM Model: {settings.mem0.llm_model}')
"
```

### **Test Memory System**
```bash
# Run the preloaded model test
poetry run python test_preloaded_model.py
```

### **Test Startup Model Preloading**
```bash
# Test that model preloading works at startup
poetry run python test_startup_preload.py

# Start application with model preloading
poetry run python -m app.main --mode server
# You should see: "✅ Sentence transformer preloaded successfully: 384 dimensions"
```

## 🎯 **Summary**

**Minimum Required Variables:**
```bash
OPENAI_API_KEY=your_openai_api_key_here
QDRANT_VECTOR_SIZE=384
MEM0_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
MEM0_MIN_RELEVANCE_SCORE=0.2
```

**All Other Variables Have Sensible Defaults** and will work out of the box for local development with Qdrant running on localhost:6333.

The memory system is now fully configurable through environment variables while maintaining backward compatibility with existing setups!
