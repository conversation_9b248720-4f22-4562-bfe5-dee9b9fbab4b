#!/usr/bin/env python3
"""
Test mem0 directly with the correct configuration.
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from mem0 import Memory as Mem0Memory
from shared.config.base import get_settings

def test_mem0_direct():
    """Test mem0 directly with correct configuration."""
    print("🧪 Testing Mem0 Direct Configuration")
    print("=" * 60)
    
    try:
        settings = get_settings()
        
        # Configure mem0 exactly as in memory service
        mem0_config = {
            "vector_store": {
                "provider": "qdrant",
                "config": {
                    "host": settings.qdrant.host,
                    "port": settings.qdrant.port,
                    "collection_name": settings.qdrant.collection_name,
                    "embedding_model_dims": 384,  # Match all-MiniLM-L6-v2 dimensions
                }
            },
            "embedder": {
                "provider": "huggingface",  # Use local sentence transformers
                "config": {
                    "model": "all-MiniLM-L6-v2",  # Standard 384 dimensions, fast and efficient
                    "embedding_dims": 384,  # Explicitly set dimensions to match model
                }
            },
            "llm": {
                "provider": "openai",
                "config": {
                    "model": settings.mem0.llm_model,
                    "api_key": settings.openai.api_key,
                }
            },
        }
        
        print("📋 Mem0 Configuration:")
        print(f"   🗄️  Vector Store: Qdrant")
        print(f"   🏠 Host: {settings.qdrant.host}:{settings.qdrant.port}")
        print(f"   📦 Collection: {settings.qdrant.collection_name}")
        print(f"   📏 Embedding Dims: 384")
        print(f"   🔤 Embedder: HuggingFace all-MiniLM-L6-v2")
        print(f"   🤖 LLM: {settings.mem0.llm_model}")
        
        print("\n🔄 Initializing Mem0...")
        mem0_client = Mem0Memory.from_config(mem0_config)
        print("✅ Mem0 initialized successfully")
        
        print("\n🔄 Testing memory addition...")
        result = mem0_client.add(
            "I am a Python developer who loves machine learning",
            user_id="test_user_direct",
            agent_id="test_agent_direct",
            metadata={"test": "direct_mem0"}
        )
        
        print(f"✅ Memory added successfully: {result}")
        
        print("\n🔄 Testing memory search...")
        search_results = mem0_client.search(
            "What programming languages do I know?",
            user_id="test_user_direct",
            agent_id="test_agent_direct",
            limit=3
        )
        
        print(f"✅ Search completed: {len(search_results)} results")
        for i, result in enumerate(search_results):
            print(f"   📋 Result {i+1}: {result.get('memory', 'N/A')} (score: {result.get('score', 0):.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing mem0 direct: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_mem0_direct()
