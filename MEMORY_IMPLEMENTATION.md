# Memory Implementation with Mem0 and Qdrant

This document describes the comprehensive memory implementation for the agent platform using Mem0 with local Qdrant database and sentence transformers for embeddings.

## Overview

The memory system provides:
- **User Query Storage**: Automatically stores all user messages
- **Agent Response Storage**: Automatically stores all agent responses  
- **Context Retrieval**: Fetches relevant memories before agent responds
- **Filtering**: Supports filtering by userId, agentId, and conversationId
- **Local Storage**: Uses local Qdrant database for vector storage
- **Sentence Transformers**: Uses local embedding models (no external API calls for embeddings)

## Architecture

```
User Query → Memory Service → Mem0 → Qdrant (Local)
                ↓
Agent Response ← Context Injection ← Relevant Memories
```

## Components

### 1. Memory Service (`app/memory/memory_service.py`)
- **Main Interface**: Unified API for all memory operations
- **Auto-extraction**: Extracts user_id and agent_id from context
- **Session Management**: Uses session_id as conversation_id when needed
- **Local Embeddings**: Uses preloaded sentence transformers
- **Smart Filtering**: Implements relevance scoring and vague query detection
- **OpenRouter Integration**: Uses OpenRouter for LLM memory processing

## Configuration

### Environment Variables

```bash
# Qdrant Configuration
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=agent_memory
QDRANT_VECTOR_SIZE=384
QDRANT_DISTANCE=Cosine
QDRANT_API_KEY=  # Optional for local instance
QDRANT_TIMEOUT=60

# Mem0 Configuration
MEM0_EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
MEM0_VECTOR_STORE_TYPE=qdrant
MEM0_LLM_PROVIDER=openai
MEM0_LLM_MODEL=gpt-4o-mini
MEM0_MEMORY_DECAY_RATE=0.01
MEM0_MAX_MEMORIES=10000

# Required for LLM operations in Mem0
OPENAI_API_KEY=your_openai_api_key_here
```

### Docker Setup

Start Qdrant locally:
```bash
docker-compose up qdrant
```

The `docker-compose.yml` includes:
- Qdrant service on port 6333
- Persistent storage volume
- Health checks

## Integration Points

### 1. Chat Processor Integration
- **User Query Storage**: Automatically stores user messages before processing
- **Context Injection**: Retrieves relevant memories and adds to agent prompt
- **Response Storage**: Stores agent responses after generation

### 2. Kafka Message Handlers
- **Agent Chat**: Integrated in `process_agent_chat`
- **Agent Query**: Integrated in `process_agent_query`  
- **Direct Messages**: Integrated in `process_agent_message`

### 3. Memory Filtering

Each memory operation includes filters:
```python
{
    "user_id": "user_123",
    "agent_id": "agent_456", 
    "conversation_id": "session_789"
}
```

## Usage Examples

### Basic Memory Operations

```python
from app.memory import memory_service

# Store user query
await memory_service.store_user_query(
    user_message="My age is 24",
    user_id="user_123",
    agent_id="agent_456",
    session_id="session_789"
)

# Store agent response
await memory_service.store_agent_response(
    agent_response="Thanks for sharing your age!",
    user_id="user_123", 
    agent_id="agent_456",
    session_id="session_789"
)

# Get relevant context
context = await memory_service.get_relevant_context(
    query="What is my age?",
    user_id="user_123",
    agent_id="agent_456", 
    session_id="session_789"
)
```

### Conversation Exchange

```python
# Store complete exchange
await memory_service.store_conversation_exchange(
    user_message="I love basketball",
    agent_response="Basketball is a great sport!",
    user_id="user_123",
    agent_id="agent_456",
    session_id="session_789"
)
```

## Memory Flow in Agent Interactions

### 1. User Sends Message
```
User: "My age is 24"
↓
Memory Service: store_user_query()
↓
Qdrant: Stores with filters {user_id, agent_id, conversation_id}
```

### 2. Agent Processes Message
```
Memory Service: get_relevant_context("My age is 24")
↓
Qdrant: Searches with filters
↓
Context: "Relevant memories: [previous conversations about age]"
↓
Agent Prompt: "Context + Current query: My age is 24"
```

### 3. Agent Responds
```
Agent: "Thanks for sharing that you're 24!"
↓
Memory Service: store_agent_response()
↓
Qdrant: Stores response with filters
```

## Testing

Run the test suite:
```bash
python test_memory_integration.py
```

The test covers:
- User query storage
- Agent response storage
- Context retrieval
- Memory filtering
- Conversation exchanges

## Dependencies

Added to `pyproject.toml`:
```toml
mem0ai = "^0.1.30"
qdrant-client = "^1.12.1" 
sentence-transformers = "^3.3.1"
```

## Benefits

1. **Persistent Memory**: Agents remember previous conversations
2. **Context-Aware Responses**: Agents can reference past interactions
3. **User-Specific**: Each user has isolated memory space
4. **Agent-Specific**: Different agents can have separate memories
5. **Conversation-Specific**: Memories can be scoped to conversations
6. **Local & Private**: All data stays on your infrastructure
7. **No External Dependencies**: Uses local embeddings and storage

## Troubleshooting

### Common Issues

1. **Qdrant Connection Error**
   - Ensure Qdrant is running: `docker-compose up qdrant`
   - Check port 6333 is accessible

2. **Embedding Model Download**
   - First run downloads sentence-transformers model
   - Ensure internet connection for initial download

3. **Memory Not Retrieved**
   - Check user_id, agent_id, conversation_id filters
   - Verify memories are being stored with correct metadata

4. **OpenAI API Key Missing**
   - Set OPENAI_API_KEY environment variable
   - Required for Mem0's LLM operations

### Logs

Enable debug logging to see memory operations:
```python
import logging
logging.getLogger("app.memory").setLevel(logging.DEBUG)
```

## Future Enhancements

- Memory decay and cleanup policies
- Memory compression for long conversations
- Advanced filtering and search capabilities
- Memory analytics and insights
- Multi-modal memory support
