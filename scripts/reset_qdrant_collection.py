#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to reset Qdrant collection with new dimensions.

This script deletes the existing collection and creates a new one
with the correct dimensions for the sentence transformer model.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "app"))

from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams
from shared.config.base import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)


def reset_qdrant_collection():
    """Reset Qdrant collection with new dimensions."""
    logger.info("🗑️  RESETTING QDRANT COLLECTION")
    logger.info("=" * 60)
    
    try:
        # Load settings
        settings = get_settings()
        collection_name = settings.qdrant.collection_name
        
        # Connect to Qdrant
        logger.info(f"🔌 Connecting to Qdrant at {settings.qdrant.host}:{settings.qdrant.port}")
        client = QdrantClient(
            host=settings.qdrant.host,
            port=settings.qdrant.port
        )
        
        # Check if collection exists
        collections = client.get_collections()
        collection_exists = any(col.name == collection_name for col in collections.collections)
        
        if collection_exists:
            logger.info(f"🗑️  Deleting existing collection: {collection_name}")
            client.delete_collection(collection_name)
            logger.info("✅ Collection deleted successfully")
        else:
            logger.info(f"ℹ️  Collection {collection_name} does not exist")
        
        # Create new collection with configured dimensions
        vector_size = settings.qdrant.vector_size
        distance_metric = getattr(Distance, settings.qdrant.distance.upper())

        logger.info(f"🆕 Creating new collection: {collection_name}")
        logger.info(f"   📏 Dimensions: {vector_size} ({settings.mem0.embedding_model})")
        logger.info(f"   📐 Distance: {settings.qdrant.distance}")

        client.create_collection(
            collection_name=collection_name,
            vectors_config=VectorParams(
                size=vector_size,
                distance=distance_metric
            )
        )
        
        logger.info("✅ Collection created successfully")
        
        # Verify collection
        collection_info = client.get_collection(collection_name)
        logger.info(f"\n📊 COLLECTION INFORMATION")
        logger.info(f"   📦 Name: {collection_info.config.params.vectors.size}")
        logger.info(f"   📏 Dimensions: {collection_info.config.params.vectors.size}")
        logger.info(f"   📐 Distance: {collection_info.config.params.vectors.distance}")
        logger.info(f"   📊 Points count: {collection_info.points_count}")
        
        logger.info(f"\n✅ QDRANT COLLECTION RESET COMPLETE")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to reset Qdrant collection: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main function."""
    logger.info("🚀 Qdrant Collection Reset Tool")
    logger.info("=" * 60)
    
    success = reset_qdrant_collection()
    
    if success:
        logger.info("\n🎉 SUCCESS!")
        logger.info("✅ Qdrant collection reset with 384 dimensions")
        logger.info("✅ Ready for all-MiniLM-L6-v2 embeddings")
        logger.info("✅ You can now start the application")
    else:
        logger.error("\n❌ FAILED!")
        logger.error("❌ Could not reset Qdrant collection")
        sys.exit(1)


if __name__ == "__main__":
    main()
