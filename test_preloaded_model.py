#!/usr/bin/env python3
"""
Test script for preloaded sentence transformer model.

This script tests that the model is preloaded at startup and
memory operations work with the new 384-dimensional embeddings.
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.startup_app import startup_models
from app.memory import memory_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)


async def test_preloaded_model():
    """Test the preloaded model and memory operations."""
    print("🧪 Testing Preloaded Sentence Transformer Model")
    print("=" * 70)
    
    # Test data
    user_id = "test_user_preloaded"
    agent_id = "test_agent_preloaded"
    session_id = "test_session_preloaded"
    
    try:
        print("\n🔄 Step 1: Testing model preloading...")
        
        # This should be very fast since model is preloaded
        start_time = time.time()
        
        await memory_service.store_user_query(
            user_message="I am a Python developer who loves machine learning",
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            metadata={"test": "preloaded_model"}
        )
        
        first_operation_time = time.time() - start_time
        print(f"   ⏱️  First memory operation: {first_operation_time:.2f} seconds")
        
        print("\n🔄 Step 2: Testing subsequent operations (should be faster)...")
        
        start_time = time.time()
        
        await memory_service.store_user_query(
            user_message="I work remotely and enjoy building AI applications",
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            metadata={"test": "preloaded_model"}
        )
        
        second_operation_time = time.time() - start_time
        print(f"   ⏱️  Second memory operation: {second_operation_time:.2f} seconds")
        
        print("\n🔄 Step 3: Testing memory retrieval...")
        
        start_time = time.time()
        
        context = await memory_service.get_relevant_context(
            query="What programming languages do I know?",
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            k=5
        )
        
        retrieval_time = time.time() - start_time
        print(f"   ⏱️  Memory retrieval: {retrieval_time:.2f} seconds")
        
        if context:
            print(f"   ✅ Retrieved context: {context[:100]}...")
        else:
            print("   ⚠️  No context retrieved")
        
        print("\n🔄 Step 4: Testing relevance filtering...")
        
        # Test vague query
        vague_context = await memory_service.get_relevant_context(
            query="hello",
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            k=5
        )
        
        if not vague_context:
            print("   ✅ Vague query correctly filtered (no context)")
        else:
            print(f"   ❌ Vague query returned context: {vague_context}")
        
        # Test specific query
        specific_context = await memory_service.get_relevant_context(
            query="What do I do for work?",
            user_id=user_id,
            agent_id=agent_id,
            session_id=session_id,
            k=5
        )
        
        if specific_context:
            print(f"   ✅ Specific query returned context: {specific_context[:100]}...")
        else:
            print("   ⚠️  Specific query returned no context")
        
        print("\n✅ Preloaded model test completed!")
        
        print(f"\n📊 PERFORMANCE SUMMARY")
        print(f"   ⏱️  First operation: {first_operation_time:.2f}s")
        print(f"   ⏱️  Second operation: {second_operation_time:.2f}s")
        print(f"   ⏱️  Retrieval: {retrieval_time:.2f}s")
        print(f"   🚀 Model: Preloaded (no loading delays)")
        print(f"   📏 Dimensions: 384 (all-MiniLM-L6-v2)")
        print(f"   🏠 Processing: Local (no API calls for embeddings)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during preloaded model test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("🚀 Preloaded Model Test Suite")
    print("=" * 70)
    
    # Check prerequisites
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key:
        print("⚠️  OPENAI_API_KEY is not set")
        print("   Please set your OpenAI API key to run this test")
        return
    
    print("✅ OPENAI_API_KEY is set")
    
    try:
        # First, preload models
        print("\n🔄 Preloading models...")
        startup_info = startup_models()
        
        print(f"\n📊 STARTUP COMPLETED")
        print(f"   ⏱️  Startup time: {startup_info['startup_time']:.2f}s")
        print(f"   📏 Model dimensions: {startup_info['dimensions']}")
        print(f"   🏠 Device: {startup_info['device']}")
        
        # Run tests
        success = await test_preloaded_model()
        
        print("\n📊 Test Results")
        print("=" * 70)
        if success:
            print("🎉 All tests passed!")
            print("\n📝 Benefits achieved:")
            print("   ✅ Model preloaded at startup (no runtime delays)")
            print("   ✅ Fast memory operations")
            print("   ✅ Local embeddings (384 dimensions)")
            print("   ✅ Relevance filtering working")
            print("   ✅ No API calls for embeddings")
            print("   ✅ Consistent performance")
        else:
            print("❌ Some tests failed!")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
