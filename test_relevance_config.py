#!/usr/bin/env python3
"""
Test script to verify min_relevance_score is loaded from environment configuration.
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.shared.config.base import get_settings

def test_relevance_config():
    """Test that min_relevance_score is loaded from configuration."""
    print("🧪 Testing Min Relevance Score Configuration")
    print("=" * 60)
    
    # Load settings
    settings = get_settings()
    
    print("📋 Current Configuration:")
    print(f"   🎯 Min Relevance Score: {settings.mem0.min_relevance_score}")
    print(f"   📏 Vector Size: {settings.qdrant.vector_size}")
    print(f"   🔤 Embedding Model: {settings.mem0.embedding_model}")
    print(f"   🤖 LLM Model: {settings.mem0.llm_model}")
    
    # Check environment variable
    env_value = os.getenv("MEM0_MIN_RELEVANCE_SCORE")
    print(f"\n🌍 Environment Variable:")
    print(f"   MEM0_MIN_RELEVANCE_SCORE = {env_value}")
    
    # Verify the value
    expected_value = 0.2
    if settings.mem0.min_relevance_score == expected_value:
        print(f"\n✅ SUCCESS: Min relevance score is correctly set to {expected_value}")
    else:
        print(f"\n❌ MISMATCH: Expected {expected_value}, got {settings.mem0.min_relevance_score}")
        
    print("\n📝 To set the environment variable:")
    print("   Add to your .env file: MEM0_MIN_RELEVANCE_SCORE=0.2")
    
    return settings.mem0.min_relevance_score == expected_value

if __name__ == "__main__":
    test_relevance_config()
