#!/usr/bin/env python3
"""
Test script to verify sentence transformer model preloading at startup.
"""

import os
import sys
import time
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.startup import preload_sentence_transformer, get_preloaded_model
from app.memory import memory_service

async def test_startup_preload():
    """Test that model preloading works correctly."""
    print("🧪 Testing Startup Model Preloading")
    print("=" * 60)
    
    # Test 1: Preload model (simulating startup)
    print("\n1️⃣ TESTING MODEL PRELOADING (simulating app startup)")
    print("-" * 50)
    
    start_time = time.time()
    try:
        model = preload_sentence_transformer()
        preload_time = time.time() - start_time
        
        print(f"✅ Model preloaded successfully in {preload_time:.2f} seconds")
        print(f"   📏 Dimensions: {model.get_sentence_embedding_dimension()}")
        print(f"   🏠 Device: {model.device}")
        
    except Exception as e:
        print(f"❌ Failed to preload model: {e}")
        return False
    
    # Test 2: Get preloaded model (should be instant)
    print("\n2️⃣ TESTING PRELOADED MODEL ACCESS")
    print("-" * 50)
    
    start_time = time.time()
    preloaded_model = get_preloaded_model()
    access_time = time.time() - start_time
    
    if preloaded_model is not None:
        print(f"✅ Preloaded model accessed in {access_time:.4f} seconds (should be ~0.0001s)")
        print(f"   📏 Dimensions: {preloaded_model.get_sentence_embedding_dimension()}")
    else:
        print("❌ Preloaded model is None")
        return False
    
    # Test 3: Memory service initialization (should use preloaded model)
    print("\n3️⃣ TESTING MEMORY SERVICE WITH PRELOADED MODEL")
    print("-" * 50)
    
    start_time = time.time()
    try:
        # This should use the preloaded model
        await memory_service._initialize()
        init_time = time.time() - start_time
        
        print(f"✅ Memory service initialized in {init_time:.2f} seconds")
        print("   🚀 Should be faster since model was preloaded!")
        
    except Exception as e:
        print(f"❌ Failed to initialize memory service: {e}")
        return False
    
    # Test 4: Verify memory operations work
    print("\n4️⃣ TESTING MEMORY OPERATIONS")
    print("-" * 50)
    
    try:
        # Store a test memory
        await memory_service.store_user_query(
            user_message="Test message for startup preload verification",
            user_id="test_startup_user",
            agent_id="test_startup_agent",
            session_id="test_startup_session"
        )
        print("✅ Memory storage successful")
        
        # Retrieve context
        context = await memory_service.get_relevant_context(
            query="test message",
            user_id="test_startup_user",
            agent_id="test_startup_agent",
            session_id="test_startup_session",
            k=1
        )
        print(f"✅ Memory retrieval successful: {len(context)} chars")
        
    except Exception as e:
        print(f"❌ Memory operations failed: {e}")
        return False
    
    print("\n🎉 ALL TESTS PASSED!")
    print("=" * 60)
    print("✅ Model preloading at startup is working correctly")
    print("✅ Memory service uses preloaded model")
    print("✅ Memory operations work with preloaded model")
    
    return True

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_startup_preload())
