#!/usr/bin/env python3
"""
Debug script to check Qdrant collections and their dimensions.
"""

import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from qdrant_client import QdrantClient
from shared.config.base import get_settings

def debug_collections():
    """Debug Qdrant collections."""
    print("🔍 DEBUGGING QDRANT COLLECTIONS")
    print("=" * 60)
    
    try:
        # Load settings
        settings = get_settings()
        
        # Connect to Qdrant
        print(f"🔌 Connecting to Qdrant at {settings.qdrant.host}:{settings.qdrant.port}")
        client = QdrantClient(
            host=settings.qdrant.host,
            port=settings.qdrant.port
        )
        
        # List all collections
        collections = client.get_collections()
        print(f"\n📦 FOUND {len(collections.collections)} COLLECTIONS:")
        
        for collection in collections.collections:
            print(f"\n   📋 Collection: {collection.name}")
            
            # Get detailed info
            try:
                info = client.get_collection(collection.name)
                print(f"      📏 Dimensions: {info.config.params.vectors.size}")
                print(f"      📐 Distance: {info.config.params.vectors.distance}")
                print(f"      📊 Points: {info.points_count}")
                print(f"      🔧 Status: {info.status}")
            except Exception as e:
                print(f"      ❌ Error getting info: {e}")
        
        # Check if our target collection exists
        target_collection = settings.qdrant.collection_name
        print(f"\n🎯 TARGET COLLECTION: {target_collection}")
        
        try:
            target_info = client.get_collection(target_collection)
            print(f"   ✅ Collection exists")
            print(f"   📏 Dimensions: {target_info.config.params.vectors.size}")
            print(f"   📐 Distance: {target_info.config.params.vectors.distance}")
            print(f"   📊 Points: {target_info.points_count}")
        except Exception as e:
            print(f"   ❌ Collection does not exist: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging collections: {e}")
        return False

if __name__ == "__main__":
    debug_collections()
