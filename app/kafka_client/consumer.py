import asyncio
import json

# Add timestamp to response chunk
from datetime import datetime
from logging import Logger
from typing import Any, Dict, Set

from aiokafka import AIOKafkaConsumer, ConsumerRecord
from sympy import true  # type: ignore

from app.shared.config.base import Settings

from ..autogen_service.agent_factory import AgentFactory
from ..autogen_service.chat_processor import ChatProcessor
from ..autogen_service.group_chat_processor import GroupChatProcessor
from ..autogen_service.orchestration_engine.orchestration_session_manager import (
    OrchestrationSessionManager,
)
from ..helper.redis_client import RedisClient
from ..helper.session_manager import SessionManager
from ..schemas.kafka import (
    AgentChatRequest,
    AgentChatResponse,
    AgentChatStopRequest,
    AgentChatStopResponse,
    AgentCreationRequest,
    AgentMessageRequest,
    AgentQueryRequest,
    AgentSessionDeletionRequest,
    AgentSessionDeletionResponse,
    AgentType,
    HumanInputResponse,
    OrchestrationTeamChatRequest,
    OrchestrationTeamSessionRequest,
)
from ..services.agent_fetch import AgentFetchService
from ..shared.config.base import get_settings
from ..shared.config.logging_config import get_logger
from ..utils.constants import ErrorCode, ErrorMessage, SSEEventType
from .producer import kafka_producer

# Get logger for this module
logger: Logger = get_logger(__name__)


class KafkaConsumer:

    def __init__(self) -> None:
        self.settings: Settings = get_settings()
        self.logger: Logger = logger

        self.kafka_agent_response_topic = self.settings.kafka.kafka_agent_response_topic

        # Initialize Kafka consumer
        self.consumer = AIOKafkaConsumer(
            self.settings.kafka.kafka_agent_creation_topic,
            self.settings.kafka.kafka_agent_chat_topic,
            self.settings.kafka.kafka_agent_query_topic,
            self.settings.kafka.kafka_agent_message_topic,
            self.settings.kafka.kafka_agent_session_deletion_topic,
            self.settings.kafka.kafka_orchestration_team_session_topic,
            self.settings.kafka.kafka_orchestration_team_chat_topic,
            self.settings.kafka.kafka_human_input_response_topic,
            self.settings.kafka.kafka_agent_chat_stop_topic,
            bootstrap_servers=self.settings.kafka.kafka_bootstrap_servers,
            group_id=self.settings.kafka.kafka_consumer_group,
            auto_offset_reset="earliest",
            enable_auto_commit=True,
        )

        # Initialize services
        self.agent_fetch_service = AgentFetchService()
        self.redis_client = RedisClient()
        self.session_manager = None  # Will be initialized in start_consumer
        self.agent_factory = AgentFactory()
        self.chat_processor = None  # Will be initialized in start_consumer
        self.group_chat_processor = None  # Will be initialized in start_consumer

        # Track active tasks to manage concurrency
        self.active_tasks: Set[asyncio.Task] = set()
        # Maximum number of concurrent message processing tasks
        self.max_concurrent_tasks: int = 10
        # Semaphore to limit concurrent processing
        self.semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        self.logger.info("Initialized successfully")

    async def start_consumer(self) -> None:
        """Start the Kafka consumer and process messages"""
        # Initialize Redis client
        await self.redis_client.initialize()

        # Initialize session manager with optimizations
        self.session_manager = SessionManager(
            self.redis_client,
            session_ttl=7200,  # 2 hour TTL
        )
        self.chat_processor = ChatProcessor(self.session_manager, self.agent_factory)
        self.group_chat_processor = GroupChatProcessor(
            self.session_manager, self.agent_factory
        )
        self.orchestration_session_manager = OrchestrationSessionManager(
            self.session_manager
        )

        # Start Kafka consumer
        await self.consumer.start()
        self.logger.info("Kafka consumer started")

        try:
            while True:
                try:
                    # Fetch batch of messages
                    batch = await self.consumer.getmany(timeout_ms=1000)

                    for tp, messages in batch.items():
                        for msg in messages:
                            # Process each message in a separate task
                            # but limit the number of concurrent tasks
                            if len(self.active_tasks) >= self.max_concurrent_tasks:
                                # Wait for at least one task to complete if we're at the limit
                                done, pending = await asyncio.wait(
                                    self.active_tasks,
                                    return_when=asyncio.FIRST_COMPLETED,
                                )
                                self.active_tasks = pending

                            # Create and start a new task for this message
                            task = asyncio.create_task(self.process_message_task(msg))
                            self.active_tasks.add(task)
                            # Remove the task from active_tasks when it completes
                            task.add_done_callback(self.active_tasks.discard)

                    # If no messages were received, wait a bit to avoid tight polling
                    if not batch:
                        await asyncio.sleep(0.1)

                except Exception as e:
                    self.logger.error(
                        "Error processing Kafka messages",
                        exc_info=True,
                        extra={"error": str(e)},
                    )
                    await asyncio.sleep(1)  # Wait before retrying

        finally:
            # Clean up resources
            await self.consumer.stop()
            await self.redis_client.close()
            self.logger.info("Kafka consumer stopped")

    async def process_message_task(self, msg: ConsumerRecord) -> None:
        """Process a Kafka message in a separate task"""
        async with self.semaphore:
            try:
                # Decode message
                message_value = json.loads(msg.value.decode("utf-8"))
                topic = msg.topic

                self.logger.info(f"Processing message from topic {topic}")

                if topic == self.settings.kafka.kafka_agent_creation_topic:
                    await self.process_agent_creation(message_value)
                elif topic == self.settings.kafka.kafka_agent_chat_topic:
                    await self.process_agent_chat(message_value)
                elif topic == self.settings.kafka.kafka_agent_query_topic:
                    await self.process_agent_query(message_value)
                elif topic == self.settings.kafka.kafka_agent_message_topic:
                    await self.process_agent_message(message_value)
                elif topic == self.settings.kafka.kafka_agent_session_deletion_topic:
                    await self.process_agent_session_deletion(message_value)
                elif (
                    topic == self.settings.kafka.kafka_orchestration_team_session_topic
                ):
                    await self.process_orchestration_team_session(message_value)
                elif topic == self.settings.kafka.kafka_orchestration_team_chat_topic:
                    await self.process_orchestration_team_chat(message_value)
                elif topic == self.settings.kafka.kafka_human_input_response_topic:
                    await self.process_human_input_response(message_value)
                elif topic == self.settings.kafka.kafka_agent_chat_stop_topic:
                    await self.process_agent_chat_stop(message_value)
                else:
                    self.logger.warning(f"Unknown topic: {topic}")

            except Exception as e:
                self.logger.error(f"Error processing message: {e}")

    async def process_agent_creation(self, message: Dict[str, Any]) -> None:
        """Process agent creation request"""

        print(f"Processing agent creation message: {message}")

        await kafka_producer.init_kafka_producer()

        headers = [
            ("correlationId", message["run_id"].encode("utf-8")),
            (
                "reply-topic",
                self.kafka_agent_response_topic.encode("utf-8"),
            ),
        ]
        try:
            # Parse request
            request = AgentCreationRequest(**message)

            # Check if this is a group chat request
            if request.agent_group_id:
                # Create group chat session
                session_id = await self.group_chat_processor.create_group_chat_session(
                    group_id=request.agent_group_id,
                    user_id=request.user_id,
                    communication_type=request.communication_type,
                    organization_id=request.organization_id,
                    use_knowledge=request.use_knowledge,
                    variables=request.variables,
                )

                if not session_id:
                    raise ValueError(
                        f"Failed to create group chat session for group: "
                        f"{request.agent_group_id}"
                    )
            else:
                # Single agent session
                # Fetch agent configuration
                agent_config = await self.agent_fetch_service.fetch_agent_config(
                    request.agent_id
                )

                print(f"Agent config: {agent_config}")

                if not agent_config:
                    raise ValueError(
                        f"Agent configuration not found for ID: {request.agent_id}"
                    )

                # Create session
                session_id = await self.session_manager.create_session(
                    agent_config=agent_config,
                    user_id=request.user_id,
                    communication_type=request.communication_type,
                    organization_id=request.organization_id,
                    use_knowledge=request.use_knowledge,
                    agent_group_id=request.agent_group_id,
                    variables=request.variables,
                    chat_context=request.conversation_context,
                )

            # Send response
            response = {
                "run_id": request.run_id,
                "session_id": session_id,
                "event_type": SSEEventType.SESSION_INITIALIZED.value,
                "success": True,
                "message": "Agent session created successfully",
            }

            await kafka_producer.send_message(
                self.settings.kafka.kafka_agent_response_topic,
                message=response,
                headers=headers,
            )

            self.logger.info(
                "Agent session created successfully",
                extra={
                    "session_id": session_id,
                    "agent_id": request.agent_id,
                    "user_id": request.user_id,
                    "run_id": request.run_id,
                },
            )

        except Exception as e:
            # Determine specific error code based on exception type and message
            error_code = ErrorCode.AGENT_CREATION_FAILED
            error_message = ErrorMessage.AGENT_SESSION_CREATION_FAILED

            if "Agent configuration not found" in str(e):
                error_code = ErrorCode.AGENT_NOT_FOUND
                error_message = ErrorMessage.AGENT_CONFIG_NOT_FOUND
            elif "Failed to create group chat session" in str(e):
                error_code = ErrorCode.GROUP_CHAT_CREATION_FAILED
                error_message = ErrorMessage.GROUP_CHAT_SESSION_CREATION_FAILED
            elif "validation" in str(e).lower():
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR

            self.logger.error(
                "Failed to create agent session",
                exc_info=True,
                extra={
                    "agent_id": message.get("agent_id", "unknown"),
                    "user_id": message.get("user_id", "unknown"),
                    "run_id": message.get("run_id", "unknown"),
                    "error_code": error_code,
                    "error": str(e),
                },
            )

            # Send error response with detailed error information
            response = {
                "run_id": message.get("run_id", "unknown"),
                "success": False,
                "message": error_message,
                "event_type": SSEEventType.ERROR.value,
                "error_code": error_code,
                "details": {
                    "original_error": str(e),
                    "agent_id": message.get("agent_id", "unknown"),
                    "user_id": message.get("user_id", "unknown"),
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                },
            }

            await kafka_producer.send_message(
                self.settings.kafka.kafka_agent_response_topic,
                message=response,
                headers=headers,
            )

    async def process_agent_chat(self, message: Dict[str, Any]) -> None:
        """Process agent chat request"""

        print(f"Processing agent chat message: {message}")

        # Initialize Kafka producer
        await kafka_producer.init_kafka_producer()

        headers = [
            ("correlationId", message["run_id"].encode("utf-8")),
            (
                "reply-topic",
                self.kafka_agent_response_topic.encode("utf-8"),
            ),
        ]
        try:
            # Parse request
            request = AgentChatRequest(**message)

            # Check if session exists
            if not await self.session_manager.session_exists(request.session_id):
                raise ValueError(f"Session not found: {request.session_id}")

            # Get session data
            (
                agent_config,
                communication_type,
                memory,
                cancellation_token,
                organization_id,
                use_knowledge,
                variables,
                user_id,
            ) = await self.session_manager.get_session_data(
                request.session_id,
            )

            user_message = (
                request.chat_context[-1]["content"] if request.chat_context else ""
            )

            # Check if this is a group chat session
            group_chat_info = await self.group_chat_processor.get_group_chat_info(
                request.session_id
            )

            if group_chat_info:
                # Process group chat
                self.logger.info(
                    f"Processing group chat for session: {request.session_id}"
                )

                # Stream group chat responses
                async for (
                    response_chunk
                ) in self.group_chat_processor.process_group_chat(
                    session_id=request.session_id,
                    user_message=user_message,
                    run_id=request.run_id,
                ):
                    # Send response chunk
                    response = AgentChatResponse(
                        run_id=request.run_id,
                        session_id=request.session_id,
                        agent_response=response_chunk,
                        success=response_chunk.get("success", False),
                        final=response_chunk.get("final", False),
                        message=response_chunk.get("message", "Group chat response"),
                        event_type=SSEEventType.MESSAGE_STREAMING.value,
                        agent_type=AgentType.USER_AGENT.value,
                    )

                    await kafka_producer.send_message(
                        self.settings.kafka.kafka_agent_response_topic,
                        response.model_dump(),
                        headers=headers,
                    )

                    # If this is the final response, break
                    if response_chunk.get("final", False):
                        break
            else:

                # Initialize agents and team
                agent = await self.agent_factory.initialize_chat_session(
                    run_id=request.run_id,
                    agent_config=agent_config,
                    session_memory=memory,
                    organization_id=organization_id,
                    user_id=user_id,
                    use_knowledge=use_knowledge,
                    variables=variables,
                )

                if request.chat_response == "message":
                    # Single agent chat

                    self.logger.info("Chat session initialized successfully")

                    # Example usage (inside an async context):
                    result = await self.chat_processor.chat_with_agent_once(
                        request.session_id,
                        user_message,
                        agent=agent,
                        run_id=request.run_id,
                        cancellation_token=cancellation_token,
                        attachments=request.attachments,
                    )

                    print(f"Result: {result}")

                    resp = AgentChatResponse(
                        run_id=request.run_id,
                        session_id=result["session_id"],
                        agent_response=result["agent_response"],
                        success=result.get("success", True),
                        final=True,  # Mark as final since we're sending one response
                        message="Chat processed successfully",
                        event_type=SSEEventType.MESSAGE_RESPONSE.value,
                        agent_type=AgentType.PERSONA_AGENT.value,
                    )

                    await kafka_producer.send_message(
                        self.kafka_agent_response_topic,
                        resp.model_dump(),
                        headers,
                    )

                else:
                    # Single agent chat with streaming support

                    self.logger.info("Chat session initialized successfully")

                    # Stream single agent chat responses
                    async for response_chunk in self.chat_processor.process_chat(
                        session_id=request.session_id,
                        user_message=user_message,
                        agent=agent,
                        run_id=request.run_id,
                        cancellation_token=cancellation_token,
                        attachments=request.attachments,
                    ):

                        response_chunk["timestamp"] = (
                            datetime.utcnow().isoformat() + "Z"
                        )

                        # Send response chunk
                        response = AgentChatResponse(
                            run_id=request.run_id,
                            session_id=request.session_id,
                            agent_response=response_chunk.get("agent_response", {}),
                            success=response_chunk.get("success", False),
                            final=response_chunk.get("final", False),
                            message=response_chunk.get(
                                "message", "Single agent chat streaming response"
                            ),
                            stream_chunk_id=response_chunk.get("stream_chunk_id", 0),
                            timestamp=response_chunk.get("timestamp"),
                            event_type=SSEEventType.MESSAGE_STREAMING.value,
                            agent_type=AgentType.PERSONA_AGENT.value,
                        )

                        await kafka_producer.send_message(
                            self.settings.kafka.kafka_agent_response_topic,
                            response.model_dump(),
                            headers=headers,
                        )

                        # Send token usage data if message_type is not streaming_chunk and models_usage is present
                        agent_response = response_chunk.get("agent_response", {})
                        message_type = agent_response.get("message_type", "")
                        models_usage = agent_response.get("models_usage")

                        if message_type != "streaming_chunk" and models_usage:
                            # Run token usage tracking in background to avoid blocking main process
                            asyncio.create_task(
                                self._send_token_usage_if_present(
                                    response_data=response_chunk,
                                    run_id=request.run_id,
                                    session_id=request.session_id,
                                    user_id=user_id,
                                    agent_id=(
                                        agent_config.get("id") if agent_config else None
                                    ),
                                    agent_type=AgentType.PERSONA_AGENT.value,
                                    organization_id=organization_id,
                                    model_id=(
                                        agent_config.get("model_data", {}).get(
                                            "model_id"
                                        )
                                        if agent_config
                                        else None
                                    ),
                                )
                            )

                        if not response_chunk.get("success", True):
                            raise ValueError(
                                f"Failed to process agent chat {response_chunk.get('error')}"
                            )

                        # If this is the final response, break
                        if response_chunk.get("final", False):
                            break

            self.logger.info(
                "Chat processing completed",
                extra={"session_id": request.session_id, "run_id": request.run_id},
            )

        except Exception as e:
            # Determine specific error code based on exception type and message
            error_code = ErrorCode.CHAT_PROCESSING_FAILED
            error_message = ErrorMessage.CHAT_PROCESSING_ERROR

            if "Session not found" in str(e):
                error_code = ErrorCode.SESSION_NOT_FOUND
                error_message = ErrorMessage.SESSION_NOT_FOUND
            elif "validation" in str(e).lower():
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR
            elif "stream" in str(e).lower():
                error_code = ErrorCode.CHAT_STREAM_ERROR
                error_message = ErrorMessage.CHAT_STREAM_ERROR

            self.logger.error(
                "Failed to process chat request",
                exc_info=True,
                extra={
                    "session_id": message.get("session_id", "unknown"),
                    "run_id": message.get("run_id", "unknown"),
                    "error_code": error_code,
                    "error": str(e),
                },
            )

            # Send error response with detailed error information
            response = AgentChatResponse(
                run_id=message.get("run_id", "unknown"),
                session_id=message.get("session_id", "unknown"),
                success=False,
                final=True,
                event_type=SSEEventType.ERROR.value,
                error_code=error_code,
                message=error_message,
                details={
                    "original_error": str(e),
                    "session_id": message.get("session_id", "unknown"),
                    "run_id": message.get("run_id", "unknown"),
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                },
                agent_response={},
            )

            await kafka_producer.send_message(
                self.settings.kafka.kafka_agent_response_topic,
                response.dict(),  # Use model_dump_json instead of json
                headers=headers,
            )

    async def process_agent_query(self, message: Dict[str, Any]) -> None:
        """Process agent query request with enhanced input fields"""
        print(f"Processing agent query message: {message}")

        # Initialize Kafka producer
        await kafka_producer.init_kafka_producer()

        headers = [
            ("correlationId", message.get("run_id", "").encode("utf-8")),
            (
                "reply-topic",
                self.kafka_agent_response_topic.encode("utf-8"),
            ),
        ]

        try:
            request = AgentQueryRequest(**message)

            if not request.agent_id or not request.query:
                raise ValueError("agent_id and query must be provided")

            # Fetch agent configuration
            agent_config = await self.agent_fetch_service.fetch_agent_config(
                request.agent_id
            )

            if not agent_config:
                raise ValueError(
                    f"Agent configuration not found for ID: {request.agent_id}"
                )

            # Create session for this query
            session_id = await self.session_manager.create_session(
                agent_config=agent_config,
                user_id=request.user_id,
                communication_type=request.communication_type,
                organization_id=request.organization_id,
                use_knowledge=request.use_knowledge,
                agent_group_id=request.agent_group_id,
                variables=request.variables,
                chat_context=request.conversation_context,
            )

            # Send quick session creation response
            session_response = {
                "run_id": request.run_id,
                "session_id": session_id,
                "event_type": SSEEventType.SESSION_INITIALIZED.value,
                "success": True,
                "message": "Session created successfully, processing query...",
                "metadata": {
                    "agent_id": request.agent_id,
                    "user_id": request.user_id,
                    "workflow_ids": request.workflow_ids,
                    "mcp_tool_ids": request.mcp_tool_ids,
                },
            }

            await kafka_producer.send_message(
                self.kafka_agent_response_topic,
                session_response,
                headers,
            )

            if request.mcp_tool_ids:
                agent_config["mcps"] = await self.agent_fetch_service.fetch_mcps_by_ids(
                    request.mcp_tool_ids
                )

            if request.workflow_ids:
                agent_config["workflows"] = (
                    await self.agent_fetch_service.fetch_workflows_by_ids(
                        request.workflow_ids
                    )
                )

            # Get session data
            (
                _,
                communication_type,
                memory,
                cancellation_token,
                organization_id,
                use_knowledge,
                variables,
                user_id,
            ) = await self.session_manager.get_session_data(
                session_id,
            )

            # Initialize agents and team
            agent = await self.agent_factory.initialize_chat_session(
                run_id=request.run_id,
                agent_config=agent_config,
                session_memory=memory,
                organization_id=request.organization_id,
                user_id=request.user_id,
                use_knowledge=request.use_knowledge,
                variables=request.variables,
            )

            # Single agent chat with streaming support

            self.logger.info("Chat session initialized successfully")

            # Stream single agent chat responses
            async for response_chunk in self.chat_processor.process_chat(
                session_id=session_id,
                user_message=request.query,
                agent=agent,
                run_id=request.run_id,
                cancellation_token=cancellation_token,
                attachments=request.attachments,
            ):

                response_chunk["timestamp"] = datetime.utcnow().isoformat() + "Z"

                # Send response chunk
                response = AgentChatResponse(
                    run_id=request.run_id,
                    session_id=session_id,
                    agent_response=response_chunk.get("agent_response", {}),
                    success=response_chunk.get("success", False),
                    final=response_chunk.get("final", False),
                    message=response_chunk.get(
                        "message", "Single agent chat streaming response"
                    ),
                    stream_chunk_id=response_chunk.get("stream_chunk_id", 0),
                    timestamp=response_chunk.get("timestamp"),
                    event_type=SSEEventType.MESSAGE_STREAMING.value,
                    agent_type=AgentType.PERSONA_AGENT.value,
                )

                await kafka_producer.send_message(
                    self.settings.kafka.kafka_agent_response_topic,
                    response.model_dump(),
                    headers=headers,
                )

                # Send token usage data if message_type is not streaming_chunk and models_usage is present
                agent_response = response_chunk.get("agent_response", {})
                message_type = agent_response.get("message_type", "")
                models_usage = agent_response.get("models_usage")

                if message_type != "streaming_chunk" and models_usage:
                    # Get agent_id from agent_config
                    config_agent_id = agent_config.get("id") if agent_config else None
                    # Run token usage tracking in background to avoid blocking main process
                    asyncio.create_task(
                        self._send_token_usage_if_present(
                            response_data=response_chunk,
                            run_id=request.run_id,
                            session_id=session_id,
                            user_id=request.user_id,
                            agent_id=config_agent_id or request.agent_id,
                            agent_type=AgentType.PERSONA_AGENT.value,
                            organization_id=request.organization_id,
                            model_id=(
                                agent_config.get("model_data", {}).get("model_id")
                                if agent_config
                                else None
                            ),
                        )
                    )

                if not response_chunk.get("success", True):
                    raise ValueError(
                        f"Failed to process agent chat {response_chunk.get('error')}"
                    )

                # If this is the final response, break
                if response_chunk.get("final", False):
                    break

            self.logger.info(
                "Chat processing completed",
                extra={"session_id": session_id, "run_id": request.run_id},
            )

        except Exception as e:
            # Determine specific error code based on exception type and message
            error_code = ErrorCode.CHAT_PROCESSING_FAILED
            error_message = ErrorMessage.CHAT_PROCESSING_ERROR

            if "Agent configuration not found" in str(e):
                error_code = ErrorCode.AGENT_NOT_FOUND
                error_message = ErrorMessage.AGENT_CONFIG_NOT_FOUND
            elif "agent_id and query must be provided" in str(e):
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR
            elif "validation" in str(e).lower():
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR

            self.logger.error(
                "Failed to process agent query request",
                exc_info=True,
                extra={
                    "session_id": message.get("session_id", "unknown"),
                    "run_id": message.get("run_id", "unknown"),
                    "agent_id": message.get("agent_id", "unknown"),
                    "error_code": error_code,
                    "error": str(e),
                },
            )

            # Send error response with detailed error information
            response = AgentChatResponse(
                run_id=message.get("run_id", "unknown"),
                session_id=message.get("session_id", "unknown"),
                success=False,
                final=True,
                event_type=SSEEventType.ERROR.value,
                error_code=error_code,
                message=error_message,
                details={
                    "original_error": str(e),
                    "agent_id": message.get("agent_id", "unknown"),
                    "run_id": message.get("run_id", "unknown"),
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                },
                agent_response={},
            )

            await kafka_producer.send_message(
                self.settings.kafka.kafka_agent_response_topic,
                response.dict(),  # Use model_dump_json instead of json
                headers=headers,
            )

    async def process_agent_message(self, message: Dict[str, Any]) -> None:
        """
        Process agent message request with provided agent configuration.
        Uses direct agent instantiation without session management overhead.
        """
        print(f"Processing agent message: {message}")

        # Initialize Kafka producer
        await kafka_producer.init_kafka_producer()

        headers = [
            ("correlationId", message.get("request_id", "").encode("utf-8")),
            (
                "reply-topic",
                self.kafka_agent_response_topic.encode("utf-8"),
            ),
        ]

        try:
            # Parse request using the new schema
            request = AgentMessageRequest(**message)

            # Validate required fields
            if not request.query:
                raise ValueError("query must be provided")

            if request.agent_config:
                agent_config_dict = {
                    "id": request.agent_config.id,
                    "name": request.agent_config.name,
                    "description": request.agent_config.description,
                    "system_message": request.agent_config.system_message,
                    "model_provider": request.agent_config.llm_model_config.model_provider,
                    "model_name": request.agent_config.llm_model_config.model,
                    "mcps": (
                        [
                            {"mcp_id": mcp.mcp_id, "tool_name": mcp.tool_name}
                            for mcp in request.agent_config.mcps
                        ]
                        if request.agent_config.mcps
                        else []
                    ),
                    "agent_type": request.agent_type.value,
                }

                try:
                    mcp_ids = [mcp["mcp_id"] for mcp in agent_config_dict["mcps"]]
                    if mcp_ids:
                        agent_config_dict["mcps"] = (
                            await self.agent_fetch_service.fetch_mcps_by_ids(mcp_ids)
                        )
                except Exception as e:
                    print(f"Error load mcps tools: {str(e)}")

            elif request.agent_id:
                # Fetch agent configuration
                agent_config_dict = await self.agent_fetch_service.fetch_agent_config(
                    request.agent_id
                )

            else:
                raise ValueError("agent_config or agent_id must be provided")

            query = request.query

            if request.variables:
                if isinstance(query, str):
                    query = self.agent_factory.process_variables(
                        query, request.variables
                    )
                if isinstance(query, list):
                    query = [
                        self.agent_factory.process_variables(q, request.variables)
                        for q in query
                    ]

            # Create agent directly without session management
            agent = await self.agent_factory.create_direct_agent(
                agent_config=agent_config_dict,
                run_id=request.request_id,
                organization_id=request.organization_id
                or agent_config_dict.get("organization_id"),
                use_knowledge=request.use_knowledge,
                variables=request.variables,
                user_id=request.user_id or agent_config_dict.get("owner_id"),
            )

            # Process message directly with the agent
            # For direct agent messages, we'll use the new multimodal support
            if request.attachments:
                # Log attachment info
                attachment_summary = ", ".join(
                    [
                        f"{att.file_name} ({att.file_type})"
                        for att in request.attachments
                    ]
                )
                self.logger.info(
                    f"Processing message with attachments: {attachment_summary}"
                )

            result = await self.chat_processor.chat_with_direct_agent(
                agent=agent,
                user_message=query,
                run_id=request.request_id,
                attachments=request.attachments,
            )

            print(f"Direct message result: {result}")

            # Prepare response
            resp = AgentChatResponse(
                request_id=request.request_id,
                run_id=request.request_id,
                session_id=result.get("session_id", request.request_id),
                agent_response=result.get("agent_response", {}),
                success=result.get("success", True),
                final=True,
                message="Message processed successfully",
                agent_type=request.agent_type.value,
                error=result.get("error", ""),
            )

            if not resp.success:
                raise ValueError(f"agent execution error: {resp.error}")

            # Send response back via Kafka
            await kafka_producer.send_message(
                self.kafka_agent_response_topic,
                resp.model_dump(),
                headers,
            )

            self.logger.info(
                "Agent message processed successfully",
                extra={
                    "request_id": request.request_id,
                    "run_id": request.run_id,
                    "user_id": request.user_id,
                    "agent_type": request.agent_type.value,
                    "execution_type": request.execution_type.value,
                },
            )

        except Exception as e:
            # Determine specific error code based on exception type and message
            error_code = ErrorCode.AGENT_EXECUTION_FAILED
            error_message = ErrorMessage.CHAT_PROCESSING_ERROR

            if "query must be provided" in str(e):
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR
            elif "validation" in str(e).lower():
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR
            elif "agent execution error" in str(e).lower():
                error_code = ErrorCode.AGENT_EXECUTION_FAILED
                error_message = ErrorMessage.AGENT_EXECUTION_FAILED
            elif "mcp" in str(e).lower():
                error_code = ErrorCode.MCP_TOOL_ERROR
                error_message = "Failed to load MCP tools"

            self.logger.error(
                "Failed to process agent message",
                exc_info=True,
                extra={
                    "request_id": message.get("request_id", "unknown"),
                    "run_id": message.get("run_id", "unknown"),
                    "user_id": message.get("user_id", "unknown"),
                    "correlation_id": message.get("correlation_id", "unknown"),
                    "error_code": error_code,
                    "error": str(e),
                },
            )

            # Send error response with detailed error information
            response = AgentChatResponse(
                run_id=message.get("run_id", message.get("request_id", "unknown")),
                session_id=message.get("run_id", message.get("request_id", "unknown")),
                success=False,
                final=True,
                event_type=SSEEventType.ERROR.value,
                error_code=error_code,
                message=error_message,
                details={
                    "original_error": str(e),
                    "request_id": message.get("request_id", "unknown"),
                    "user_id": message.get("user_id", "unknown"),
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                },
                agent_response={},
                request_id=message.get("request_id"),
            )

            await kafka_producer.send_message(
                self.settings.kafka.kafka_agent_response_topic,
                response.model_dump(),
                headers=headers,
            )

    async def process_agent_session_deletion(self, message: Dict[str, Any]) -> None:
        """
        Process agent session deletion request.
        Deletes the specified session and cleans up associated resources.
        """
        print(f"Processing session deletion message: {message}")

        # Initialize Kafka producer
        await kafka_producer.init_kafka_producer()

        headers = [
            ("correlationId", message.get("run_id", "").encode("utf-8")),
            (
                "reply-topic",
                self.kafka_agent_response_topic.encode("utf-8"),
            ),
        ]

        try:
            # Parse request using the session deletion schema
            request = AgentSessionDeletionRequest(**message)

            # Validate required fields
            if not request.session_id:
                raise ValueError("session_id must be provided")

            # Check if session exists
            session_exists = await self.session_manager.session_exists(
                request.session_id
            )

            if not session_exists and not request.force:
                raise ValueError(f"Session not found: {request.session_id}")

            # Delete the session
            deletion_success = False
            if session_exists:
                deletion_success = await self.session_manager.delete_session(
                    request.session_id
                )
            else:
                # If force is True and session doesn't exist, consider it successful
                deletion_success = request.force

            if not deletion_success:
                raise ValueError(f"Failed to delete session: {request.session_id}")

            deleted_at = datetime.utcnow().isoformat() + "Z"

            # Prepare success response
            resp = AgentSessionDeletionResponse(
                run_id=request.run_id,
                session_id=request.session_id,
                success=True,
                message=f"Session deleted successfully. Reason: {request.reason}",
                deleted_at=deleted_at,
            )

            # Send response back via Kafka
            await kafka_producer.send_message(
                self.kafka_agent_response_topic,
                resp.model_dump(),
                headers,
            )

            self.logger.info(
                "Session deleted successfully",
                extra={
                    "session_id": request.session_id,
                    "run_id": request.run_id,
                    "user_id": request.user_id,
                    "reason": request.reason,
                    "deleted_at": deleted_at,
                },
            )

        except Exception as e:
            # Determine specific error code based on exception type and message
            error_code = ErrorCode.SESSION_DELETION_FAILED
            error_message = ErrorMessage.SESSION_DELETION_ERROR

            if "Session not found" in str(e):
                error_code = ErrorCode.SESSION_NOT_FOUND
                error_message = ErrorMessage.SESSION_NOT_FOUND
            elif "session_id must be provided" in str(e):
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR
            elif "validation" in str(e).lower():
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR

            self.logger.error(
                "Failed to delete session",
                exc_info=True,
                extra={
                    "session_id": message.get("session_id", "unknown"),
                    "run_id": message.get("run_id", "unknown"),
                    "user_id": message.get("user_id", "unknown"),
                    "error_code": error_code,
                    "error": str(e),
                },
            )

            # Send error response with detailed error information
            response = AgentSessionDeletionResponse(
                run_id=message.get("run_id", "unknown"),
                session_id=message.get("session_id", "unknown"),
                success=False,
                message=error_message,
                deleted_at=None,
                error_code=error_code,
                event_type=SSEEventType.ERROR.value,
                details={
                    "original_error": str(e),
                    "session_id": message.get("session_id", "unknown"),
                    "user_id": message.get("user_id", "unknown"),
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                },
            )

            await kafka_producer.send_message(
                self.settings.kafka.kafka_agent_response_topic,
                response.model_dump(),
                headers=headers,
            )

    async def process_orchestration_team_session(self, message: Dict[str, Any]) -> None:
        """Process orchestration team session creation request"""
        print(f"Processing orchestration team session creation: {message}")

        # Initialize Kafka producer
        await kafka_producer.init_kafka_producer()

        headers = [
            ("correlationId", message.get("run_id", "").encode("utf-8")),
            (
                "reply-topic",
                self.kafka_agent_response_topic.encode("utf-8"),
            ),
        ]

        try:
            # Parse request
            request = OrchestrationTeamSessionRequest(**message)

            # Create orchestration session
            session_id = (
                await self.orchestration_session_manager.create_orchestration_session(
                    user_id=request.user_id,
                    organization_id=request.organization_id,
                    variables=request.variables,
                    chat_context=request.conversation_context,
                )
            )

            # Send response
            response = {
                "run_id": request.run_id,
                "session_id": session_id,
                "event_type": SSEEventType.SESSION_INITIALIZED.value,
                "success": True,
                "message": "Orchestration team session created successfully",
            }

            await kafka_producer.send_message(
                self.settings.kafka.kafka_agent_response_topic,
                message=response,
                headers=headers,
            )

            self.logger.info(
                "Orchestration team session created successfully",
                extra={
                    "session_id": session_id,
                    "run_id": request.run_id,
                    "user_id": request.user_id,
                },
            )

        except Exception as e:
            # Determine specific error code based on exception type and message
            error_code = ErrorCode.ORCHESTRATION_SESSION_FAILED
            error_message = ErrorMessage.ORCHESTRATION_SESSION_ERROR

            if "validation" in str(e).lower():
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR

            self.logger.error(
                "Failed to create orchestration team session",
                exc_info=True,
                extra={
                    "run_id": message.get("run_id", "unknown"),
                    "user_id": message.get("user_id", "unknown"),
                    "error_code": error_code,
                    "error": str(e),
                },
            )

            # Send error response with detailed error information
            response = {
                "run_id": message.get("run_id", "unknown"),
                "success": False,
                "message": error_message,
                "event_type": SSEEventType.ERROR.value,
                "error_code": error_code,
                "details": {
                    "original_error": str(e),
                    "user_id": message.get("user_id", "unknown"),
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                },
            }

            await kafka_producer.send_message(
                self.settings.kafka.kafka_agent_response_topic,
                message=response,
                headers=headers,
            )

    async def process_orchestration_team_chat(self, message: Dict[str, Any]) -> None:
        """Process orchestration team chat request with human-in-the-loop support"""
        print(f"Processing orchestration team chat message: {message}")

        # Initialize Kafka producer
        await kafka_producer.init_kafka_producer()

        headers = [
            ("correlationId", message.get("run_id", "").encode("utf-8")),
            (
                "reply-topic",
                self.kafka_agent_response_topic.encode("utf-8"),
            ),
        ]

        try:
            # Parse request
            request = OrchestrationTeamChatRequest(**message)

            # Check if session exists, if not create orchestration session
            if request.session_id:
                session_exists = await self.session_manager.session_exists(
                    request.session_id
                )
                if not session_exists:
                    raise ValueError(f"Session not found: {request.session_id}")

            # Get session data
            (
                agent_config,
                communication_type,
                memory,
                cancellation_token,
                organization_id,
                use_knowledge,
                variables,
                user_id,
            ) = await self.session_manager.get_session_data(
                request.session_id,
            )

            # Log attachment info if present
            if request.attachments:
                attachment_summary = ", ".join(
                    [
                        f"{att.file_name} ({att.file_type})"
                        for att in request.attachments
                    ]
                )
                self.logger.info(
                    f"Processing orchestration team chat with attachments: {attachment_summary}"
                )

            tools = []
            if request.tools:
                tools = await self.agent_fetch_service.fetch_mcps_by_ids(request.tools)

            # Process orchestration team chat with streaming
            async for (
                response_chunk
            ) in self.orchestration_session_manager.process_orchestration_chat(
                session_id=request.session_id,
                user_message=request.user_message,
                run_id=request.run_id,
                mode=request.mode,
                attachments=request.attachments,
                tools=tools,
                resource=request.resource,
            ):
                # Send response chunk
                response = AgentChatResponse(
                    run_id=request.run_id,
                    session_id=request.session_id,
                    agent_response=response_chunk.get("agent_response", {}),
                    success=response_chunk.get("success", False),
                    final=response_chunk.get("final", False),
                    message=response_chunk.get(
                        "message", "Orchestration team response"
                    ),
                    event_type=SSEEventType.MESSAGE_STREAMING.value,
                    agent_type=AgentType.GLOBAL_AGENT.value,
                )

                await kafka_producer.send_message(
                    self.settings.kafka.kafka_agent_response_topic,
                    response.model_dump(),
                    headers=headers,
                )

                # Send token usage data if message_type is not streaming_chunk and models_usage is present
                agent_response = response_chunk.get("agent_response", {})
                message_type = agent_response.get("message_type", "")
                models_usage = agent_response.get("models_usage")

                if message_type != "streaming_chunk" and models_usage:
                    # Run token usage tracking in background to avoid blocking main process
                    asyncio.create_task(
                        self._send_token_usage_if_present(
                            response_data=response_chunk,
                            run_id=request.run_id,
                            session_id=request.session_id or "unknown",
                            user_id=user_id,
                            agent_id=None,  # Orchestration teams use multiple agents
                            agent_type=AgentType.GLOBAL_AGENT.value,
                            organization_id=organization_id,
                            model_id=getattr(
                                self.settings, "ORCHESTRATION_TEAM_CHAT_MODEL_ID", ""
                            ),
                        )
                    )

                if not response_chunk.get("success", true):
                    raise ValueError(
                        f"Failed to process orchestration team chat {response_chunk.get('error')}"
                    )

                # If this is the final response, break
                if response_chunk.get("final", False):
                    break

            self.logger.info(
                "Orchestration team chat processing completed",
                extra={"session_id": request.session_id, "run_id": request.run_id},
            )

        except Exception as e:
            # Determine specific error code based on exception type and message
            error_code = ErrorCode.ORCHESTRATION_CHAT_FAILED
            error_message = ErrorMessage.ORCHESTRATION_CHAT_ERROR

            if "Session not found" in str(e):
                error_code = ErrorCode.SESSION_NOT_FOUND
                error_message = ErrorMessage.SESSION_NOT_FOUND
            elif "validation" in str(e).lower():
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR

            self.logger.error(
                "Failed to process orchestration team chat request",
                exc_info=True,
                extra={
                    "session_id": message.get("session_id", "unknown"),
                    "run_id": message.get("run_id", "unknown"),
                    "error_code": error_code,
                    "error": str(e),
                },
            )

            # Send error response with detailed error information
            response = AgentChatResponse(
                run_id=message.get("run_id", "unknown"),
                session_id=message.get("session_id", "unknown"),
                message=error_message,
                success=False,
                final=True,
                event_type=SSEEventType.ERROR.value,
                error_code=error_code,
                details={
                    "original_error": str(e),
                    "session_id": message.get("session_id", "unknown"),
                    "run_id": message.get("run_id", "unknown"),
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                },
                agent_response={},
            )

            await kafka_producer.send_message(
                self.settings.kafka.kafka_agent_response_topic,
                response.model_dump(),
                headers=headers,
            )

    async def process_human_input_response(self, message: Dict[str, Any]) -> None:
        """Process human input response for team conversations"""
        print(f"Processing human input response: {message}")

        try:
            # Parse request
            request = HumanInputResponse(**message)

            # Handle the human input response
            success = (
                await self.orchestration_session_manager.handle_human_input_response(
                    team_conversation_id=request.team_conversation_id,
                    user_input=request.user_input,
                )
            )

            if success:
                self.logger.info(
                    "Human input response processed successfully",
                    extra={
                        "session_id": request.session_id,
                        "run_id": request.run_id,
                        "team_conversation_id": request.team_conversation_id,
                    },
                )
            else:
                self.logger.warning(
                    "No pending human input request found",
                    extra={
                        "session_id": request.session_id,
                        "run_id": request.run_id,
                        "team_conversation_id": request.team_conversation_id,
                    },
                )

        except Exception as e:
            # Determine specific error code based on exception type and message
            error_code = ErrorCode.HUMAN_INPUT_PROCESSING_FAILED
            error_message = ErrorMessage.HUMAN_INPUT_ERROR

            if "validation" in str(e).lower():
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR

            self.logger.error(
                "Failed to process human input response",
                exc_info=True,
                extra={
                    "session_id": message.get("session_id", "unknown"),
                    "run_id": message.get("run_id", "unknown"),
                    "error_code": error_code,
                    "error": str(e),
                    "details": {
                        "original_error": str(e),
                        "session_id": message.get("session_id", "unknown"),
                        "run_id": message.get("run_id", "unknown"),
                        "timestamp": datetime.utcnow().isoformat() + "Z",
                    },
                },
            )

    async def process_agent_chat_stop(self, message: Dict[str, Any]) -> None:
        """
        Process agent chat stop request.
        Stops running agent chat sessions and cancels ongoing operations.
        """
        print(f"Processing agent chat stop message: {message}")

        # Initialize Kafka producer
        await kafka_producer.init_kafka_producer()

        headers = [
            ("correlationId", message.get("run_id", "").encode("utf-8")),
            (
                "reply-topic",
                self.kafka_agent_response_topic.encode("utf-8"),
            ),
        ]

        try:
            # Parse request using the chat stop schema
            request = AgentChatStopRequest(**message)

            # Validate required fields
            if not request.run_id:
                raise ValueError("run_id must be provided")

            stopped_sessions = []

            stopped_at = datetime.utcnow().isoformat() + "Z"

            if request.session_id:
                # Stop specific session
                success = await self._stop_single_session(request.session_id)
                if success:
                    stopped_sessions.append(request.session_id)

            else:
                raise ValueError(f"Invalid session_id: {request.session_id}")

            # Prepare success response
            resp = AgentChatStopResponse(
                run_id=request.run_id,
                session_id=request.session_id,
                success=True,
                message=f"Successfully stopped {len(stopped_sessions)} session(s). Reason: {request.reason}",
                stopped_sessions=stopped_sessions,
                stopped_at=stopped_at,
            )

            # Send response back via Kafka
            await kafka_producer.send_message(
                self.kafka_agent_response_topic,
                resp.model_dump(),
                headers,
            )

            self.logger.info(
                "Chat stop request processed successfully",
                extra={
                    "run_id": request.run_id,
                    "user_id": request.user_id,
                    "stopped_sessions": stopped_sessions,
                    "reason": request.reason,
                    "stopped_at": stopped_at,
                },
            )

        except Exception as e:
            # Determine specific error code based on exception type and message
            error_code = ErrorCode.SESSION_STOP_ERROR
            error_message = ErrorMessage.SESSION_STOP_ERROR

            if "run_id must be provided" in str(e):
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR
            elif "Invalid session_id" in str(e):
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR
            elif "validation" in str(e).lower():
                error_code = ErrorCode.VALIDATION_ERROR
                error_message = ErrorMessage.VALIDATION_ERROR

            self.logger.error(
                "Failed to process chat stop request",
                exc_info=True,
                extra={
                    "run_id": message.get("run_id", "unknown"),
                    "user_id": message.get("user_id", "unknown"),
                    "error_code": error_code,
                    "error": str(e),
                },
            )

            # Send error response with detailed error information
            response = AgentChatStopResponse(
                run_id=message.get("run_id", "unknown"),
                session_id=message.get("session_id"),
                success=False,
                message=error_message,
                stopped_sessions=[],
                stopped_at=None,
                error_code=error_code,
                event_type=SSEEventType.ERROR.value,
                details={
                    "original_error": str(e),
                    "run_id": message.get("run_id", "unknown"),
                    "user_id": message.get("user_id", "unknown"),
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                },
            )

            await kafka_producer.send_message(
                self.kafka_agent_response_topic,
                response.model_dump(),
                headers=headers,
            )

    async def _stop_single_session(self, session_id: str) -> bool:
        """
        Stop a single chat session and cancel its operations.

        Args:
            session_id: The session ID to stop

        Returns:
            True if successful, False otherwise
        """
        try:
            self.logger.info(f"Stopping session: {session_id}")

            # Check if session exists
            session_exists = await self.session_manager.session_exists(session_id)
            if not session_exists:
                self.logger.warning(
                    f"Session {session_id} not found, considering as already stopped"
                )
                return True

            # Cancel any ongoing operations using cancellation token
            cancellation_token = self.session_manager.cancellation_tokens.get(
                session_id
            )

            print("cancellation_token", cancellation_token)

            if cancellation_token and not cancellation_token.is_cancelled():
                cancellation_token.cancel()
                self.logger.info(
                    f"Cancelled ongoing operations for session: {session_id}"
                )

            # Force cleanup session from chat processor
            if self.chat_processor:
                await self.chat_processor.force_cleanup_session(session_id)

            # End group chat session if it exists
            if self.group_chat_processor:
                group_chat_info = await self.group_chat_processor.get_group_chat_info(
                    session_id
                )
                if group_chat_info:
                    await self.group_chat_processor.end_group_chat_session(session_id)

        except Exception as e:
            self.logger.error(f"Error stopping session {session_id}: {e}")
            return False

    async def _send_token_usage_if_present(
        self,
        response_data: Dict[str, Any],
        run_id: str,
        session_id: str,
        user_id: str | None = None,
        agent_id: str | None = None,
        organization_id: str | None = None,
        request_id: str | None = None,
        agent_type: str | None = None,
        model_id: str | None = None,
    ) -> None:
        """
        Extract token usage from response data and send it via Kafka producer if present.

        Args:
            response_data: The response data that may contain token usage
            run_id: The run ID from the request (used as event_id)
            session_id: The session ID
            user_id: The user ID (optional)
            agent_id: The agent ID (optional)
            organization_id: The organization ID (optional)
            request_id: The request ID (optional)
            agent_type: The agent type (optional)
            model_id: The model ID (optional)
        """
        try:
            # Initialize Kafka producer if not already done
            await kafka_producer.init_kafka_producer()

            # Extract token usage from various possible locations in the response
            models_usage = None

            # Check in agent_response.models_usage
            if isinstance(response_data, dict):
                agent_response = response_data.get("agent_response", {})

                if isinstance(agent_response, dict):
                    models_usage = agent_response.get("models_usage")

                    # Also check in metadata.models_usage
                    if not models_usage:
                        metadata = agent_response.get("metadata", {})
                        if isinstance(metadata, dict):
                            models_usage = metadata.get("models_usage")

                # If not found in agent_response, check top level
                if not models_usage:
                    models_usage = response_data.get("models_usage")

            # If token usage is found, send it
            if models_usage and isinstance(models_usage, dict):
                prompt_tokens = models_usage.get("prompt_tokens", 0)
                completion_tokens = models_usage.get("completion_tokens", 0)

                # Also check for alternative token field names
                if prompt_tokens == 0:
                    prompt_tokens = models_usage.get("input_tokens", 0)
                if completion_tokens == 0:
                    completion_tokens = models_usage.get("output_tokens", 0)

                # Only send token usage if we have actual model_id and agent_id or non-zero tokens
                if (
                    model_id
                    and agent_id
                    or (prompt_tokens > 0 or completion_tokens > 0)
                ):
                    await kafka_producer.send_token_usage(
                        event_id=run_id,  # Use run_id as event_id (both are UUID4)
                        organisation_id=organization_id,  # Fixed spelling: organisation instead of organization
                        user_id=user_id or "unknown",
                        input_tokens=prompt_tokens,
                        output_tokens=completion_tokens,
                        model_id=model_id,
                        agent_id=agent_id,
                    )

                    self.logger.info(
                        f"📊 Token usage sent: run_id={run_id}, "
                        f"input_tokens={prompt_tokens}, output_tokens={completion_tokens}, "
                        f"model_id={model_id}, agent_id={agent_id}"
                    )
            else:
                # Log only when we have a valid response but no token usage
                if isinstance(response_data, dict) and response_data.get(
                    "agent_response", {}
                ).get("source") not in ["user", "system"]:
                    self.logger.debug(
                        f"No token usage found for agent response: {models_usage}"
                    )

        except Exception as e:
            self.logger.error(
                f"Error sending token usage for run_id {run_id}: {e}",
                exc_info=True,
            )


async def consume() -> None:
    """Start the Kafka consumer"""
    consumer_instance = KafkaConsumer()
    return await consumer_instance.start_consumer()
