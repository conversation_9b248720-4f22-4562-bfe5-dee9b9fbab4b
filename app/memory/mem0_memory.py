"""
Mem0 Memory implementation for AutoGen agents.

This module provides a Mem0-based memory implementation that follows
AutoGen's Memory protocol for storing and retrieving agent memories
using local Qdrant and sentence transformers.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional

from autogen_core.memory import Memory, MemoryContent, MemoryMimeType
from autogen_core.model_context import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ontext
from mem0 import Memory as Mem0Memory
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
from sentence_transformers import SentenceTransformer

from ..shared.config.base import get_settings

logger = logging.getLogger(__name__)


class Mem0AgentMemory(Memory):
    """
    Mem0-based memory implementation for AutoGen agents.

    This class implements the AutoGen Memory protocol using Mem0 with
    local Qdrant as the vector database backend and sentence transformers
    for embeddings.
    """

    def __init__(
        self,
        agent_id: str,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        k: int = 5,
        score_threshold: float = 0.7,
    ):
        """
        Initialize Mem0 memory.

        Args:
            agent_id: Unique identifier for the agent
            user_id: Optional user identifier for multi-tenant scenarios
            conversation_id: Optional conversation identifier
            k: Number of top results to return in queries
            score_threshold: Minimum similarity score for retrieval
        """
        self.settings = get_settings()
        self.agent_id = agent_id
        self.user_id = user_id
        self.conversation_id = conversation_id
        self.k = k
        self.score_threshold = score_threshold
        
        self._initialized = False
        self._mem0_client = None
        self._qdrant_client = None
        self._embedding_model = None

    async def _initialize(self) -> None:
        """Initialize Mem0 client and dependencies."""
        if self._initialized:
            return

        try:
            # Initialize Qdrant client
            self._qdrant_client = QdrantClient(
                host=self.settings.qdrant.host,
                port=self.settings.qdrant.port,
                api_key=self.settings.qdrant.api_key,
                timeout=self.settings.qdrant.timeout,
            )

            # Initialize embedding model
            self._embedding_model = SentenceTransformer(
                self.settings.mem0.embedding_model
            )

            # Configure Mem0 with Qdrant
            mem0_config = {
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "host": self.settings.qdrant.host,
                        "port": self.settings.qdrant.port,
                        "collection_name": self.settings.qdrant.collection_name,
                    }
                },
                "embedder": {
                    "provider": "sentence_transformers",
                    "config": {
                        "model": self.settings.mem0.embedding_model,
                    }
                },
                "llm": {
                    "provider": self.settings.mem0.llm_provider,
                    "config": {
                        "model": self.settings.mem0.llm_model,
                        "api_key": self.settings.openai.api_key,
                    }
                }
            }

            # Initialize Mem0 client
            self._mem0_client = Mem0Memory.from_config(mem0_config)

            # Ensure collection exists
            await self._ensure_collection_exists()

            self._initialized = True
            logger.info(f"Mem0 memory initialized for agent: {self.agent_id}")

        except Exception as e:
            logger.error(f"Error initializing Mem0 memory: {e}")
            raise

    async def _ensure_collection_exists(self) -> None:
        """Ensure the Qdrant collection exists."""
        try:
            collection_name = self.settings.qdrant.collection_name
            
            # Check if collection exists
            collections = self._qdrant_client.get_collections()
            collection_exists = any(
                col.name == collection_name for col in collections.collections
            )

            if not collection_exists:
                # Create collection
                self._qdrant_client.create_collection(
                    collection_name=collection_name,
                    vectors_config=VectorParams(
                        size=self.settings.qdrant.vector_size,
                        distance=Distance.COSINE,
                    ),
                )
                logger.info(f"Created Qdrant collection: {collection_name}")

        except Exception as e:
            logger.error(f"Error ensuring collection exists: {e}")
            raise

    def _create_metadata(self, memory: MemoryContent) -> Dict[str, Any]:
        """Create metadata for memory storage."""
        metadata = {
            "agent_id": self.agent_id,
            "user_id": self.user_id or "",
            "conversation_id": self.conversation_id or "",
            "mime_type": memory.mime_type.value if memory.mime_type else "text",
            "timestamp": asyncio.get_event_loop().time(),
        }

        # Add custom metadata
        if memory.metadata:
            metadata.update(memory.metadata)

        return metadata

    def _create_filters(self) -> Dict[str, Any]:
        """Create filters for memory retrieval."""
        filters = {"agent_id": self.agent_id}
        
        if self.user_id:
            filters["user_id"] = self.user_id
            
        if self.conversation_id:
            filters["conversation_id"] = self.conversation_id
            
        return filters

    async def add(self, memory: MemoryContent) -> None:
        """
        Add a memory to the Mem0 store.

        Args:
            memory: The memory content to add
        """
        if not self._initialized:
            await self._initialize()

        try:
            # Create metadata with filters
            metadata = self._create_metadata(memory)

            # Add memory using Mem0
            self._mem0_client.add(
                messages=memory.content,
                user_id=self.user_id or "default",
                agent_id=self.agent_id,
                metadata=metadata,
            )

            logger.debug(f"Added memory to Mem0 for agent: {self.agent_id}")

        except Exception as e:
            logger.error(f"Error adding memory to Mem0: {e}")
            raise

    async def query(self, query: str, k: Optional[int] = None) -> List[MemoryContent]:
        """
        Query memories from Mem0.

        Args:
            query: The query string
            k: Number of results to return (defaults to self.k)

        Returns:
            List of relevant memory contents
        """
        if not self._initialized:
            await self._initialize()

        try:
            # Search memories using Mem0
            results = self._mem0_client.search(
                query=query,
                user_id=self.user_id or "default",
                agent_id=self.agent_id,
                limit=k or self.k,
            )

            memories = []
            for result in results:
                # Extract content and metadata
                content = result.get("memory", "")
                metadata = result.get("metadata", {})
                
                # Filter by score threshold
                score = result.get("score", 0.0)
                if score < self.score_threshold:
                    continue

                # Create MemoryContent
                memory_content = MemoryContent(
                    content=content,
                    mime_type=MemoryMimeType.TEXT,
                    metadata={**metadata, "score": score},
                )
                memories.append(memory_content)

            logger.debug(f"Retrieved {len(memories)} memories for query: {query}")
            return memories

        except Exception as e:
            logger.error(f"Error querying memories from Mem0: {e}")
            return []

    async def get_context(self, context: ChatCompletionContext) -> str:
        """
        Get relevant context for chat completion.

        Args:
            context: The chat completion context

        Returns:
            Relevant context string
        """
        if not context.messages:
            return ""

        # Use the last message as query
        last_message = context.messages[-1]
        query_content = getattr(last_message, "content", "")
        
        if not query_content:
            return ""

        # Query relevant memories
        memories = await self.query(query_content)
        
        if not memories:
            return ""

        # Format memories as context
        context_parts = []
        for memory in memories:
            context_parts.append(f"Memory: {memory.content}")

        return "\n".join(context_parts)

    async def clear(self) -> None:
        """Clear all memories for this agent."""
        if not self._initialized:
            await self._initialize()

        try:
            # Get all memories for this agent
            all_memories = self._mem0_client.get_all(
                user_id=self.user_id or "default",
                agent_id=self.agent_id,
            )

            # Delete each memory
            for memory in all_memories:
                memory_id = memory.get("id")
                if memory_id:
                    self._mem0_client.delete(memory_id)

            logger.info(f"Cleared all memories for agent: {self.agent_id}")

        except Exception as e:
            logger.error(f"Error clearing memories: {e}")
            raise

    async def close(self) -> None:
        """Close the memory and cleanup resources."""
        try:
            if self._qdrant_client:
                self._qdrant_client.close()
            
            self._initialized = False
            logger.debug("Closed Mem0 memory")
            
        except Exception as e:
            logger.error(f"Error closing Mem0 memory: {e}")
