"""
Mem0 Memory Manager for managing agent memories and knowledge.

This module provides high-level management functions for Mem0-based
agent memories, including knowledge indexing and retrieval with
proper filtering by user, agent, and conversation.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from autogen_core.memory import MemoryContent, MemoryMimeType
from .mem0_memory import Mem0AgentMemory
from ..shared.config.base import get_settings

logger = logging.getLogger(__name__)


class Mem0MemoryManager:
    """
    High-level manager for Mem0-based agent memories.

    Provides utilities for managing agent knowledge, memories, and
    context across different agents, users, and conversations.
    """

    def __init__(self):
        self.settings = get_settings()
        self._memory_instances: Dict[str, Mem0AgentMemory] = {}

    def get_agent_memory(
        self,
        agent_id: str,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        **kwargs,
    ) -> Mem0AgentMemory:
        """
        Get or create a Mem0 memory instance for an agent.

        Args:
            agent_id: Unique identifier for the agent
            user_id: Optional user identifier
            conversation_id: Optional conversation identifier
            **kwargs: Additional arguments for Mem0AgentMemory

        Returns:
            Mem0AgentMemory instance for the agent
        """
        # Create a unique key for this memory instance
        memory_key = f"{agent_id}:{user_id or 'global'}:{conversation_id or 'default'}"

        if memory_key not in self._memory_instances:
            self._memory_instances[memory_key] = Mem0AgentMemory(
                agent_id=agent_id,
                user_id=user_id,
                conversation_id=conversation_id,
                **kwargs
            )

        return self._memory_instances[memory_key]

    async def add_user_message(
        self,
        user_message: str,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Add a user message to memory.

        Args:
            user_message: The user's message
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier
            metadata: Optional additional metadata

        Returns:
            True if successful, False otherwise
        """
        try:
            memory = self.get_agent_memory(
                agent_id=agent_id or "default",
                user_id=user_id,
                conversation_id=conversation_id
            )

            # Prepare metadata
            msg_metadata = metadata or {}
            msg_metadata.update({
                "type": "user_message",
                "user_id": user_id,
                "agent_id": agent_id or "default",
                "conversation_id": conversation_id or "default",
                "timestamp": datetime.utcnow().isoformat(),
                "source": "user",
            })

            memory_content = MemoryContent(
                content=user_message,
                mime_type=MemoryMimeType.TEXT,
                metadata=msg_metadata,
            )

            await memory.add(memory_content)
            logger.debug(f"Added user message to memory for user: {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error adding user message to memory: {e}")
            return False

    async def add_agent_response(
        self,
        agent_response: str,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Add an agent response to memory.

        Args:
            agent_response: The agent's response
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier
            metadata: Optional additional metadata

        Returns:
            True if successful, False otherwise
        """
        try:
            memory = self.get_agent_memory(
                agent_id=agent_id or "default",
                user_id=user_id,
                conversation_id=conversation_id
            )

            # Prepare metadata
            resp_metadata = metadata or {}
            resp_metadata.update({
                "type": "agent_response",
                "user_id": user_id,
                "agent_id": agent_id or "default",
                "conversation_id": conversation_id or "default",
                "timestamp": datetime.utcnow().isoformat(),
                "source": "agent",
            })

            memory_content = MemoryContent(
                content=agent_response,
                mime_type=MemoryMimeType.TEXT,
                metadata=resp_metadata,
            )

            await memory.add(memory_content)
            logger.debug(f"Added agent response to memory for agent: {agent_id}")
            return True

        except Exception as e:
            logger.error(f"Error adding agent response to memory: {e}")
            return False

    async def add_conversation_memory(
        self,
        user_message: str,
        agent_response: str,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Add a complete conversation exchange to memory.

        Args:
            user_message: The user's message
            agent_response: The agent's response
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier
            metadata: Optional additional metadata

        Returns:
            True if successful, False otherwise
        """
        try:
            # Add user message
            await self.add_user_message(
                user_message=user_message,
                user_id=user_id,
                agent_id=agent_id,
                conversation_id=conversation_id,
                metadata=metadata,
            )

            # Add agent response
            await self.add_agent_response(
                agent_response=agent_response,
                user_id=user_id,
                agent_id=agent_id,
                conversation_id=conversation_id,
                metadata=metadata,
            )

            logger.debug(f"Added conversation memory for user: {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error adding conversation memory: {e}")
            return False

    async def get_relevant_memories(
        self,
        query: str,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        k: int = 5,
    ) -> List[MemoryContent]:
        """
        Get relevant memories for a query with proper filtering.

        Args:
            query: The query string
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier
            k: Number of results to return

        Returns:
            List of relevant memory contents
        """
        try:
            memory = self.get_agent_memory(
                agent_id=agent_id or "default",
                user_id=user_id,
                conversation_id=conversation_id
            )

            memories = await memory.query(query, k=k)
            logger.debug(f"Retrieved {len(memories)} relevant memories for query")
            return memories

        except Exception as e:
            logger.error(f"Error getting relevant memories: {e}")
            return []

    async def format_memories_as_context(
        self,
        memories: List[MemoryContent],
        max_length: int = 2000,
    ) -> str:
        """
        Format memories as context string for agent prompts.

        Args:
            memories: List of memory contents
            max_length: Maximum length of context string

        Returns:
            Formatted context string
        """
        if not memories:
            return ""

        context_parts = []
        current_length = 0

        for memory in memories:
            # Format memory with metadata
            memory_type = memory.metadata.get("type", "unknown")
            timestamp = memory.metadata.get("timestamp", "")
            source = memory.metadata.get("source", "unknown")
            
            memory_text = f"[{memory_type}] ({source}): {memory.content}"
            
            # Check if adding this memory would exceed max length
            if current_length + len(memory_text) > max_length:
                break
                
            context_parts.append(memory_text)
            current_length += len(memory_text)

        if context_parts:
            return "Relevant memories:\n" + "\n".join(context_parts)
        return ""

    async def clear_agent_memory(
        self,
        agent_id: str,
        user_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
    ) -> bool:
        """
        Clear an agent's memory.

        Args:
            agent_id: Agent identifier
            user_id: Optional user identifier
            conversation_id: Optional conversation identifier

        Returns:
            True if successful, False otherwise
        """
        try:
            memory = self.get_agent_memory(
                agent_id=agent_id,
                user_id=user_id,
                conversation_id=conversation_id
            )

            await memory.clear()
            logger.info(f"Cleared memory for agent: {agent_id}")
            return True

        except Exception as e:
            logger.error(f"Error clearing agent memory: {e}")
            return False

    async def close_all_memories(self):
        """Close all memory instances."""
        for memory in self._memory_instances.values():
            await memory.close()
        self._memory_instances.clear()
        logger.info("Closed all memory instances")


# Global memory manager instance
mem0_memory_manager = Mem0MemoryManager()
