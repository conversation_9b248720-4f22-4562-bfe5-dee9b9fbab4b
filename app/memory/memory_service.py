"""
Memory Service Layer for Agent Platform.

This module provides a unified interface for memory operations across
the entire application, handling both user queries and agent responses
with proper filtering and context management.
"""

import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

from mem0 import Memory as Mem0Memory
from ..shared.config.base import get_settings

logger = logging.getLogger(__name__)


class MemoryService:
    """
    Unified memory service for the agent platform.
    
    Provides a single interface for all memory operations including
    storing user queries, agent responses, and retrieving relevant
    context for agent interactions.
    """

    def __init__(self):
        self.settings = get_settings()
        self._mem0_client = None
        self._initialized = False

    async def _initialize(self) -> None:
        """Initialize Mem0 client."""
        if self._initialized:
            return

        try:
            # Configure Mem0 with Qdrant
            mem0_config = {
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "host": self.settings.qdrant.host,
                        "port": self.settings.qdrant.port,
                        "collection_name": self.settings.qdrant.collection_name,
                    }
                },
                "embedder": {
                    "provider": "openai",  # Use OpenAI for embeddings for now
                    "config": {
                        "model": "text-embedding-3-small",
                        "api_key": self.settings.openai.api_key,
                    }
                },
                "llm": {
                    "provider": self.settings.mem0.llm_provider,
                    "config": {
                        "model": self.settings.mem0.llm_model,
                        "api_key": self.settings.openai.api_key,
                    }
                }
            }

            # Initialize Mem0 client
            self._mem0_client = Mem0Memory.from_config(mem0_config)
            self._initialized = True
            logger.info("Memory service initialized with Mem0")

        except Exception as e:
            logger.error(f"Error initializing memory service: {e}")
            raise

    async def store_user_query(
        self,
        user_message: str,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Store a user query in memory.

        Args:
            user_message: The user's message/query
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier
            session_id: Optional session identifier (used as conversation_id if conversation_id is None)
            metadata: Optional additional metadata

        Returns:
            True if successful, False otherwise
        """
        try:
            if not self._initialized:
                await self._initialize()

            # Use session_id as conversation_id if conversation_id is not provided
            conv_id = conversation_id or session_id

            # Prepare metadata
            query_metadata = metadata or {}
            query_metadata.update({
                "session_id": session_id,
                "query_type": "user_input",
                "stored_at": datetime.utcnow().isoformat(),
                "agent_id": agent_id or "default",
                "conversation_id": conv_id or "default",
            })

            # Add memory using Mem0
            self._mem0_client.add(
                messages=user_message,
                user_id=user_id,
                agent_id=agent_id or "default",
                metadata=query_metadata,
            )

            logger.debug(f"Stored user query for user: {user_id}, agent: {agent_id}")
            return True

        except Exception as e:
            logger.error(f"Error storing user query: {e}")
            return False

    async def store_agent_response(
        self,
        agent_response: str,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Store an agent response in memory.

        Args:
            agent_response: The agent's response
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier
            session_id: Optional session identifier (used as conversation_id if conversation_id is None)
            metadata: Optional additional metadata

        Returns:
            True if successful, False otherwise
        """
        try:
            if not self._initialized:
                await self._initialize()

            # Use session_id as conversation_id if conversation_id is not provided
            conv_id = conversation_id or session_id

            # Prepare metadata
            response_metadata = metadata or {}
            response_metadata.update({
                "session_id": session_id,
                "response_type": "agent_output",
                "stored_at": datetime.utcnow().isoformat(),
                "agent_id": agent_id or "default",
                "conversation_id": conv_id or "default",
            })

            # Add memory using Mem0
            self._mem0_client.add(
                messages=agent_response,
                user_id=user_id,
                agent_id=agent_id or "default",
                metadata=response_metadata,
            )

            logger.debug(f"Stored agent response for user: {user_id}, agent: {agent_id}")
            return True

        except Exception as e:
            logger.error(f"Error storing agent response: {e}")
            return False

    async def store_conversation_exchange(
        self,
        user_message: str,
        agent_response: str,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Store a complete conversation exchange (user message + agent response).

        Args:
            user_message: The user's message
            agent_response: The agent's response
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier
            session_id: Optional session identifier
            metadata: Optional additional metadata

        Returns:
            True if successful, False otherwise
        """
        try:
            # Store user message
            user_success = await self.store_user_query(
                user_message=user_message,
                user_id=user_id,
                agent_id=agent_id,
                conversation_id=conversation_id,
                session_id=session_id,
                metadata=metadata,
            )

            # Store agent response
            agent_success = await self.store_agent_response(
                agent_response=agent_response,
                user_id=user_id,
                agent_id=agent_id,
                conversation_id=conversation_id,
                session_id=session_id,
                metadata=metadata,
            )

            success = user_success and agent_success
            if success:
                logger.debug(f"Stored conversation exchange for user: {user_id}")
            else:
                logger.warning(f"Partial failure storing conversation exchange for user: {user_id}")

            return success

        except Exception as e:
            logger.error(f"Error storing conversation exchange: {e}")
            return False

    async def get_relevant_context(
        self,
        query: str,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        k: int = 5,
        max_context_length: int = 2000,
    ) -> str:
        """
        Get relevant context for a query to provide to the agent.

        Args:
            query: The query string to find relevant memories for
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier
            session_id: Optional session identifier
            k: Number of memories to retrieve
            max_context_length: Maximum length of context string

        Returns:
            Formatted context string for the agent
        """
        try:
            if not self._initialized:
                await self._initialize()

            # Use session_id as conversation_id if conversation_id is not provided
            conv_id = conversation_id or session_id

            # Search memories using Mem0
            results = self._mem0_client.search(
                query=query,
                user_id=user_id,
                agent_id=agent_id or "default",
                limit=k,
            )

            # Format memories as context
            context_parts = []
            current_length = 0

            for result in results:
                memory_text = result.get("memory", "")
                if not memory_text:
                    continue

                # Check if adding this memory would exceed max length
                if current_length + len(memory_text) > max_context_length:
                    break

                context_parts.append(f"Memory: {memory_text}")
                current_length += len(memory_text)

            context = ""
            if context_parts:
                context = "Relevant memories:\n" + "\n".join(context_parts)

            if context:
                logger.debug(f"Retrieved context for query: {query[:50]}...")
            else:
                logger.debug("No relevant context found for query")

            return context

        except Exception as e:
            logger.error(f"Error getting relevant context: {e}")
            return ""

    async def extract_user_id_from_context(
        self,
        context: Union[str, Dict, List],
        default_user_id: str = "default_user",
    ) -> str:
        """
        Extract user ID from various context formats.

        Args:
            context: Context that might contain user information
            default_user_id: Default user ID if none found

        Returns:
            Extracted or default user ID
        """
        try:
            if isinstance(context, dict):
                # Check common user ID fields
                for field in ["user_id", "userId", "user", "id"]:
                    if field in context:
                        return str(context[field])
            elif isinstance(context, list) and context:
                # Check if it's a list of messages with user info
                for item in context:
                    if isinstance(item, dict):
                        for field in ["user_id", "userId", "user", "id"]:
                            if field in item:
                                return str(item[field])

            return default_user_id

        except Exception as e:
            logger.error(f"Error extracting user ID from context: {e}")
            return default_user_id

    async def extract_agent_id_from_context(
        self,
        context: Union[str, Dict, List],
        default_agent_id: Optional[str] = None,
    ) -> Optional[str]:
        """
        Extract agent ID from various context formats.

        Args:
            context: Context that might contain agent information
            default_agent_id: Default agent ID if none found

        Returns:
            Extracted or default agent ID
        """
        try:
            if isinstance(context, dict):
                # Check common agent ID fields
                for field in ["agent_id", "agentId", "agent", "agent_name"]:
                    if field in context:
                        return str(context[field])
            elif isinstance(context, list) and context:
                # Check if it's a list of messages with agent info
                for item in context:
                    if isinstance(item, dict):
                        for field in ["agent_id", "agentId", "agent", "agent_name"]:
                            if field in item:
                                return str(item[field])

            return default_agent_id

        except Exception as e:
            logger.error(f"Error extracting agent ID from context: {e}")
            return default_agent_id

    async def clear_memory(
        self,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
    ) -> bool:
        """
        Clear memory for specific filters.

        Args:
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier

        Returns:
            True if successful, False otherwise
        """
        try:
            if not self._initialized:
                await self._initialize()

            # Get all memories for this user/agent combination
            all_memories = self._mem0_client.get_all(
                user_id=user_id,
                agent_id=agent_id or "default",
            )

            # Delete each memory
            for memory in all_memories:
                memory_id = memory.get("id")
                if memory_id:
                    self._mem0_client.delete(memory_id)

            logger.info(f"Cleared memory for user: {user_id}, agent: {agent_id}")
            return True

        except Exception as e:
            logger.error(f"Error clearing memory: {e}")
            return False


# Global memory service instance
memory_service = MemoryService()
