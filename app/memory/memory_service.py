"""
Memory Service Layer for Agent Platform.

This module provides a unified interface for memory operations across
the entire application, handling both user queries and agent responses
with proper filtering and context management.
"""

import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timezone

from mem0 import Memory as Mem0Memory
from ..shared.config.base import get_settings
from ..startup import ensure_model_loaded, get_preloaded_model

logger = logging.getLogger(__name__)


class MemoryService:
    """
    Unified memory service for the agent platform.
    
    Provides a single interface for all memory operations including
    storing user queries, agent responses, and retrieving relevant
    context for agent interactions.
    """

    def __init__(self):
        self.settings = get_settings()
        self._mem0_client = None
        self._initialized = False

    async def _initialize(self) -> None:
        """Initialize Mem0 client."""
        if self._initialized:
            return

        try:
            # Ensure sentence transformer model is loaded
            logger.info("🔄 Ensuring sentence transformer model is loaded...")
            model = ensure_model_loaded()
            logger.info(f"✅ Sentence transformer ready: {model.get_sentence_embedding_dimension()} dimensions")

            # Configure Mem0 with Qdrant
            mem0_config = {
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "host": self.settings.qdrant.host,
                        "port": self.settings.qdrant.port,
                        "collection_name": self.settings.qdrant.collection_name,
                        "embedding_model_dims": self.settings.qdrant.vector_size,  # Use configured dimensions
                    }
                },
                "embedder": {
                    "provider": "huggingface",  # Use local sentence transformers
                    "config": {
                        "model": self.settings.mem0.embedding_model.replace("sentence-transformers/", ""),  # Remove prefix for mem0
                        "embedding_dims": self.settings.qdrant.vector_size,  # Use configured dimensions
                    }
                },
                "llm": {
                    "provider": "openai",
                    "config": {
                        "model": self.settings.mem0.llm_model,
                        "api_key": self.settings.openai.api_key,
                    }
                }
            }

            # Initialize Mem0 client
            logger.info("🔄 Initializing Mem0 with 384-dimensional embeddings...")
            logger.info(f"   📦 Collection: {self.settings.qdrant.collection_name}")
            logger.info(f"   📏 Embedding dims: 384")
            self._mem0_client = Mem0Memory.from_config(mem0_config)
            self._initialized = True
            logger.info("🧠 MEMORY SERVICE INITIALIZED")
            logger.info(f"   🗄️  Vector Store: Qdrant ({self.settings.qdrant.host}:{self.settings.qdrant.port})")
            logger.info(f"   🔤 Embedder: HuggingFace {self.settings.mem0.embedding_model} (LOCAL sentence transformer, {self.settings.qdrant.vector_size} dims)")
            logger.info(f"   🤖 LLM: {self.settings.mem0.llm_model} (for memory extraction & processing)")
            logger.info(f"   📦 Collection: {self.settings.qdrant.collection_name}")
            logger.info(f"   ℹ️  OpenAI API is only used for: Memory processing (LLM)")
            logger.info(f"   🏠 Embeddings are generated LOCALLY (no API calls for embeddings)")

        except Exception as e:
            logger.error(f"Error initializing memory service: {e}")
            raise

    async def store_user_query(
        self,
        user_message: str,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Store a user query in memory.

        Args:
            user_message: The user's message/query
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier
            session_id: Optional session identifier (used as conversation_id if conversation_id is None)
            metadata: Optional additional metadata

        Returns:
            True if successful, False otherwise
        """
        try:
            if not self._initialized:
                await self._initialize()

            # Use session_id as conversation_id if conversation_id is not provided
            conv_id = conversation_id or session_id

            # Prepare metadata
            query_metadata = metadata or {}
            query_metadata.update({
                "session_id": session_id,
                "query_type": "user_input",
                "stored_at": datetime.now(timezone.utc).isoformat(),
                "agent_id": agent_id or "default",
                "conversation_id": conv_id or "default",
            })

            # Add memory using Mem0
            self._mem0_client.add(
                messages=user_message,
                user_id=user_id,
                agent_id=agent_id or "default",
                metadata=query_metadata,
            )

            logger.info(f"🧠 MEMORY STORED - USER QUERY")
            logger.info(f"   📝 Content: {user_message[:100]}{'...' if len(user_message) > 100 else ''}")
            logger.info(f"   👤 User ID: {user_id}")
            logger.info(f"   🤖 Agent ID: {agent_id or 'default'}")
            logger.info(f"   💬 Session ID: {session_id}")
            logger.info(f"   📊 Metadata: {query_metadata}")
            return True

        except Exception as e:
            logger.error(f"❌ ERROR STORING USER QUERY")
            logger.error(f"   🚨 Error: {str(e)}")
            logger.error(f"   👤 User ID: {user_id}")
            logger.error(f"   🤖 Agent ID: {agent_id}")
            logger.error(f"   💬 Session ID: {session_id}")
            import traceback
            logger.error(f"   📋 Traceback: {traceback.format_exc()}")
            return False

    async def store_agent_response(
        self,
        agent_response: str,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Store an agent response in memory.

        Args:
            agent_response: The agent's response
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier
            session_id: Optional session identifier (used as conversation_id if conversation_id is None)
            metadata: Optional additional metadata

        Returns:
            True if successful, False otherwise
        """
        try:
            if not self._initialized:
                await self._initialize()

            # Use session_id as conversation_id if conversation_id is not provided
            conv_id = conversation_id or session_id

            # Prepare metadata
            response_metadata = metadata or {}
            response_metadata.update({
                "session_id": session_id,
                "response_type": "agent_output",
                "stored_at": datetime.now(timezone.utc).isoformat(),
                "agent_id": agent_id or "default",
                "conversation_id": conv_id or "default",
            })

            # Add memory using Mem0
            self._mem0_client.add(
                messages=agent_response,
                user_id=user_id,
                agent_id=agent_id or "default",
                metadata=response_metadata,
            )

            logger.info(f"🧠 MEMORY STORED - AGENT RESPONSE")
            logger.info(f"   🤖 Content: {agent_response[:100]}{'...' if len(agent_response) > 100 else ''}")
            logger.info(f"   👤 User ID: {user_id}")
            logger.info(f"   🤖 Agent ID: {agent_id or 'default'}")
            logger.info(f"   💬 Session ID: {session_id}")
            logger.info(f"   📊 Metadata: {response_metadata}")
            return True

        except Exception as e:
            logger.error(f"❌ ERROR STORING AGENT RESPONSE")
            logger.error(f"   🚨 Error: {str(e)}")
            logger.error(f"   👤 User ID: {user_id}")
            logger.error(f"   🤖 Agent ID: {agent_id}")
            logger.error(f"   💬 Session ID: {session_id}")
            import traceback
            logger.error(f"   📋 Traceback: {traceback.format_exc()}")
            return False

    async def store_conversation_exchange(
        self,
        user_message: str,
        agent_response: str,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Store a complete conversation exchange (user message + agent response).

        Args:
            user_message: The user's message
            agent_response: The agent's response
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier
            session_id: Optional session identifier
            metadata: Optional additional metadata

        Returns:
            True if successful, False otherwise
        """
        try:
            # Store user message
            user_success = await self.store_user_query(
                user_message=user_message,
                user_id=user_id,
                agent_id=agent_id,
                conversation_id=conversation_id,
                session_id=session_id,
                metadata=metadata,
            )

            # Store agent response
            agent_success = await self.store_agent_response(
                agent_response=agent_response,
                user_id=user_id,
                agent_id=agent_id,
                conversation_id=conversation_id,
                session_id=session_id,
                metadata=metadata,
            )

            success = user_success and agent_success
            if success:
                logger.info(f"🎯 CONVERSATION EXCHANGE COMPLETED")
                logger.info(f"   ✅ User query stored: {user_success}")
                logger.info(f"   ✅ Agent response stored: {agent_success}")
                logger.info(f"   👤 User ID: {user_id}")
                logger.info(f"   🤖 Agent ID: {agent_id or 'default'}")
                logger.info(f"   💬 Session ID: {session_id}")
            else:
                logger.warning(f"❌ PARTIAL FAILURE in conversation exchange for user: {user_id}")
                logger.warning(f"   User query stored: {user_success}")
                logger.warning(f"   Agent response stored: {agent_success}")

            return success

        except Exception as e:
            logger.error(f"Error storing conversation exchange: {e}")
            return False

    async def get_relevant_context(
        self,
        query: str,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        k: int = 5,
        max_context_length: int = 2000,
        min_relevance_score: float = 0.40,  # Balanced threshold for sentence transformers
    ) -> str:
        """
        Get relevant context for a query to provide to the agent.

        Args:
            query: The query string to find relevant memories for
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier
            session_id: Optional session identifier
            k: Number of memories to retrieve
            max_context_length: Maximum length of context string
            min_relevance_score: Minimum similarity score to include memory

        Returns:
            Formatted context string for the agent
        """
        try:
            if not self._initialized:
                await self._initialize()

            # Use session_id as conversation_id if conversation_id is not provided
            conv_id = conversation_id or session_id

            # Check if query is too vague/generic to warrant context injection
            if self._is_vague_query(query):
                logger.info(f"🚫 SKIPPING CONTEXT - Query too vague: {query[:50]}...")
                return ""

            # Search memories using Mem0 with conversation filtering
            search_filters = {
                "user_id": user_id,
                "agent_id": agent_id or "default",
            }

            # Add conversation_id filter if provided for conversation isolation
            if conv_id:
                search_filters["conversation_id"] = conv_id

            results = self._mem0_client.search(
                query=query,
                user_id=user_id,
                agent_id=agent_id or "default",
                limit=k,
                filters=search_filters,
            )

            logger.info(f"🔍 MEMORY SEARCH PERFORMED")
            logger.info(f"   🔎 Query: {query[:100]}{'...' if len(query) > 100 else ''}")
            logger.info(f"   👤 User ID: {user_id}")
            logger.info(f"   🤖 Agent ID: {agent_id or 'default'}")
            logger.info(f"   💬 Session ID: {session_id}")
            logger.info(f"   �️  Conversation ID: {conv_id}")
            logger.info(f"   �📊 Results found: {len(results)}")

            # Format memories as context
            context_parts = []
            current_length = 0

            for i, result in enumerate(results):
                memory_text = result.get("memory", "")
                score = result.get("score", 0.0)
                if not memory_text:
                    continue

                logger.info(f"   📋 Memory {i+1}: {memory_text[:80]}{'...' if len(memory_text) > 80 else ''} (score: {score:.3f})")

                # Filter out memories with low relevance scores
                if score < min_relevance_score:
                    logger.info(f"   🚫 Skipping memory {i+1} - score {score:.3f} below threshold {min_relevance_score}")
                    continue

                # Check if adding this memory would exceed max length
                if current_length + len(memory_text) > max_context_length:
                    logger.info(f"   ⚠️  Truncating context at {current_length} chars (max: {max_context_length})")
                    break

                context_parts.append(f"Memory: {memory_text}")
                current_length += len(memory_text)

            context = ""
            if context_parts:
                context = "Relevant memories:\n" + "\n".join(context_parts)

            if context:
                logger.info(f"🎯 CONTEXT RETRIEVED ({len(context_parts)} memories, {current_length} chars)")
                logger.info(f"   📝 Context preview: {context[:150]}{'...' if len(context) > 150 else ''}")
            else:
                logger.info("❌ NO RELEVANT CONTEXT FOUND")

            return context

        except Exception as e:
            logger.error(f"Error getting relevant context: {e}")
            return ""

    def _is_vague_query(self, query: str) -> bool:
        """
        Check if a query is too vague/generic to warrant context injection.

        Args:
            query: The user query to analyze

        Returns:
            True if query is vague, False if specific enough for context
        """
        query_lower = query.lower().strip()

        # Very short queries (likely greetings or simple responses)
        if len(query_lower) < 5:
            return True

        # Exact matches for common greetings and generic responses
        exact_vague_patterns = [
            # Greetings
            "hello", "hi", "hey", "good morning", "good afternoon", "good evening",
            "how are you", "what's up", "sup", "greetings", "hey there",

            # Generic responses
            "yes", "no", "ok", "okay", "sure", "thanks", "thank you", "bye", "goodbye",
            "see you", "talk to you later", "ttyl", "got it", "understood", "alright",

            # Single word responses
            "cool", "nice", "great", "awesome", "perfect", "exactly", "right", "what",
        ]

        # Check for exact matches or very similar patterns
        for pattern in exact_vague_patterns:
            if query_lower == pattern:
                return True
            # Check for greetings with minor additions like "hi there", "hello!"
            if pattern in ["hello", "hi", "hey", "bye", "thanks"] and pattern in query_lower and len(query_lower) < 15:
                return True

        # Check for greeting patterns with additional words but still vague
        greeting_starters = ["hi ", "hello ", "hey ", "good morning", "good afternoon", "good evening"]
        for starter in greeting_starters:
            if query_lower.startswith(starter):
                # If it's just a greeting with "how are you" or similar, it's vague
                remaining = query_lower[len(starter):].strip()
                if remaining in ["", "there", "how are you", "how are you doing", "how's it going"]:
                    return True

        # Don't block questions that contain specific question words with context
        question_indicators = ["what is my", "what's my", "how old", "where do", "what do", "tell me about"]
        for indicator in question_indicators:
            if indicator in query_lower:
                return False  # These are specific questions, not vague

        return False

    async def extract_user_id_from_context(
        self,
        context: Union[str, Dict, List],
        default_user_id: str = "default_user",
    ) -> str:
        """
        Extract user ID from various context formats.

        Args:
            context: Context that might contain user information
            default_user_id: Default user ID if none found

        Returns:
            Extracted or default user ID
        """
        try:
            if isinstance(context, dict):
                # Check common user ID fields
                for field in ["user_id", "userId", "user", "id"]:
                    if field in context:
                        return str(context[field])
            elif isinstance(context, list) and context:
                # Check if it's a list of messages with user info
                for item in context:
                    if isinstance(item, dict):
                        for field in ["user_id", "userId", "user", "id"]:
                            if field in item:
                                return str(item[field])

            return default_user_id

        except Exception as e:
            logger.error(f"Error extracting user ID from context: {e}")
            return default_user_id

    async def extract_agent_id_from_context(
        self,
        context: Union[str, Dict, List],
        default_agent_id: Optional[str] = None,
    ) -> Optional[str]:
        """
        Extract agent ID from various context formats.

        Args:
            context: Context that might contain agent information
            default_agent_id: Default agent ID if none found

        Returns:
            Extracted or default agent ID
        """
        try:
            if isinstance(context, dict):
                # Check common agent ID fields
                for field in ["agent_id", "agentId", "agent", "agent_name"]:
                    if field in context:
                        return str(context[field])
            elif isinstance(context, list) and context:
                # Check if it's a list of messages with agent info
                for item in context:
                    if isinstance(item, dict):
                        for field in ["agent_id", "agentId", "agent", "agent_name"]:
                            if field in item:
                                return str(item[field])

            return default_agent_id

        except Exception as e:
            logger.error(f"Error extracting agent ID from context: {e}")
            return default_agent_id

    async def clear_memory(
        self,
        user_id: str,
        agent_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
    ) -> bool:
        """
        Clear memory for specific filters.

        Args:
            user_id: User identifier
            agent_id: Optional agent identifier
            conversation_id: Optional conversation identifier

        Returns:
            True if successful, False otherwise
        """
        try:
            if not self._initialized:
                await self._initialize()

            # Get all memories for this user/agent combination
            all_memories = self._mem0_client.get_all(
                user_id=user_id,
                agent_id=agent_id or "default",
            )

            # Delete each memory
            for memory in all_memories:
                memory_id = memory.get("id")
                if memory_id:
                    self._mem0_client.delete(memory_id)

            logger.info(f"🧹 MEMORY CLEARED")
            logger.info(f"   👤 User ID: {user_id}")
            logger.info(f"   🤖 Agent ID: {agent_id}")
            logger.info(f"   🗂️  Conversation ID: {conversation_id}")
            logger.info(f"   📊 Memories deleted: {len(all_memories)}")
            return True

        except Exception as e:
            logger.error(f"Error clearing memory: {e}")
            return False


# Global memory service instance
memory_service = MemoryService()
