import asyncio
import json
import logging
import time
from contextlib import asynccontextmanager
from dataclasses import dataclass
from typing import Any, AsyncGenerator, Dict, List, Optional, Union

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.messages import (
    MultiModalMessage,
    StructuredMessage,
    TextMessage,
)
from autogen_core import CancellationToken
from pydantic import BaseModel

from ..helper.session_manager import SessionManager
from ..schemas.chat import AgentResponse, MessageType
from ..schemas.kafka import MessageAttachment
from ..utils.file_processor import FileProcessor
from .agent_factory import AgentFactory
from .message_processor import MessageProcessor
from ..memory import memory_service

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class ChatSession:
    """Data class for chat session tracking"""

    session_id: str
    start_time: float
    last_activity: float

    def is_expired(self, timeout: float) -> bool:
        """Check if session is expired"""
        return time.time() - self.start_time > timeout

    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = time.time()


class ChatProcessor:
    """Optimized chat processor with improved session management and error handling"""

    def __init__(self, session_manager: SessionManager, agent_factory: AgentFactory):
        self.session_manager = session_manager
        self.agent_factory = agent_factory
        self.logger = logger
        self.message_processor = MessageProcessor()
        self.file_processor = FileProcessor()
        self.memory_service = memory_service

        # Session tracking with improved data structure
        self._active_sessions: Dict[str, ChatSession] = {}
        self._session_lock = asyncio.Lock()
        self._chat_timeout = 300  # 5 minutes timeout

        # Start cleanup task
        self._cleanup_task = asyncio.create_task(self._periodic_cleanup())

    async def _periodic_cleanup(self):
        """Periodic cleanup of expired sessions"""
        while True:
            try:
                await asyncio.sleep(60)  # Run every minute
                await self._cleanup_expired_sessions()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in periodic cleanup: {e}")

    async def _cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        expired_sessions = []

        async with self._session_lock:
            for session_id, session in list(self._active_sessions.items()):
                if session.is_expired(self._chat_timeout):
                    expired_sessions.append(session_id)

            for session_id in expired_sessions:
                del self._active_sessions[session_id]
                self.logger.warning(f"Cleaned up expired session: {session_id}")

    @asynccontextmanager
    async def _session_context(self, session_id: str):
        """Context manager for session tracking"""
        session = ChatSession(
            session_id=session_id, start_time=time.time(), last_activity=time.time()
        )

        async with self._session_lock:
            self._active_sessions[session_id] = session

        try:
            yield session
        finally:
            async with self._session_lock:
                self._active_sessions.pop(session_id, None)

    async def _is_session_active(self, session_id: str) -> bool:
        """Check if session is active"""
        async with self._session_lock:
            return session_id in self._active_sessions

    def _create_user_input_patch(self, user_message: str):
        """Create patched user input function for autogen v0.4"""

        async def _patched_user_input(prompt, cancellation_token=None):
            """
            Patched input function that returns the user message instead of
            prompting for input. This prevents EOF errors in server environments.
            """
            self.logger.debug(f"UserProxy input patched, returning: {user_message}")
            return user_message

        return _patched_user_input

    def _patch_user_proxy_agents(self, obj, user_message: str):
        """
        Recursively patch UserProxyAgent input function for autogen v0.4

        In autogen v0.4, UserProxyAgent has different internal structure.
        We need to patch both 'input' and '_get_input' methods to handle
        different scenarios.
        """
        patched_input = self._create_user_input_patch(user_message)

        if isinstance(obj, UserProxyAgent):
            # Patch the main input method
            obj.input = patched_input

            # Also patch the internal _get_input method if it exists
            if hasattr(obj, "_get_input"):
                obj._get_input = patched_input

            # Set human_input_mode to NEVER to prevent input prompts
            if hasattr(obj, "human_input_mode"):
                obj.human_input_mode = "NEVER"

            self.logger.debug(
                f"Patched UserProxyAgent: {getattr(obj, 'name', 'unknown')}"
            )

        elif hasattr(obj, "participants"):
            # Handle team objects with participants
            for participant in getattr(obj, "participants", []):
                self._patch_user_proxy_agents(participant, user_message)

        elif hasattr(obj, "_participants"):
            # Handle some team objects that use _participants
            for participant in getattr(obj, "_participants", []):
                self._patch_user_proxy_agents(participant, user_message)

    def create_safe_user_proxy_agent(
        self, name: str = "user_proxy", user_message: str = ""
    ) -> UserProxyAgent:
        """
        Create a UserProxyAgent configured to prevent EOF errors in server
        environments.

        This method creates a UserProxyAgent with proper input handling that
        won't try to read from stdin, preventing EOF errors when running in
        non-interactive environments like servers or APIs.
        """
        try:
            # Create UserProxyAgent with safe defaults
            user_proxy = UserProxyAgent(name=name)

            # Patch the input method immediately
            user_proxy.input = self._create_user_input_patch(user_message)

            # Set human_input_mode to NEVER to prevent input prompts
            if hasattr(user_proxy, "human_input_mode"):
                user_proxy.human_input_mode = "NEVER"

            # Also patch the internal _get_input method if it exists
            if hasattr(user_proxy, "_get_input"):
                user_proxy._get_input = self._create_user_input_patch(user_message)

            self.logger.debug(f"Created safe UserProxyAgent: {name}")
            return user_proxy

        except Exception as e:
            self.logger.error(f"Failed to create safe UserProxyAgent: {e}")
            raise

    async def _process_message_response(
        self, response: Any, agent: AssistantAgent
    ) -> Optional[AgentResponse]:
        """Process a single message response"""

        print(f"Processing response: {response}")

        msg_type: MessageType = self.message_processor.get_message_type(response)

        source, content, metadata = self.message_processor.extract_message_content(
            response
        )

        return AgentResponse(
            content=content,
            source=source,
            models_usage=self.message_processor.extract_models_usage(response),
            message_type=msg_type.value,
            metadata=metadata,
        )

    async def _update_session_memory(
        self, session_id: str, role: str, content: Union[str, List, Dict]
    ):
        """Update session memory with error handling"""
        try:
            await self.session_manager.update_session_memory(
                session_id=session_id, message={"role": role, "content": content}
            )
        except Exception as e:
            self.logger.error(f"Failed to update session memory for {session_id}: {e}")

    async def _get_session_identifiers(self, session_id: str) -> tuple[str, Optional[str]]:
        """Get user_id and agent_id from session data"""
        try:
            session_data = await self.session_manager.get_session_data(session_id)
            user_id = session_data[7] if len(session_data) > 7 else "default_user"  # user_id is at index 7

            # Try to get agent_id from agent_config - prioritize ID over name
            agent_config = session_data[0] if len(session_data) > 0 else None
            agent_id = None
            if agent_config and hasattr(agent_config, 'id') and agent_config.id:
                agent_id = agent_config.id
            elif agent_config and hasattr(agent_config, 'name'):
                agent_id = agent_config.name
            elif agent_config and isinstance(agent_config, dict):
                agent_id = agent_config.get('id') or agent_config.get('agent_id') or agent_config.get('name')

            return user_id or "default_user", agent_id
        except Exception as e:
            self.logger.error(f"Error getting session identifiers: {e}")
            return "default_user", None

    async def _create_message_from_content(
        self, user_message: str, attachments: Optional[List[MessageAttachment]] = None
    ) -> Union[TextMessage, MultiModalMessage]:
        """
        Create appropriate message type based on content and attachments

        Args:
            user_message: Text content of the message
            attachments: Optional list of file attachments

        Returns:
            TextMessage if no attachments, MultiModalMessage if attachments present
        """
        if not attachments:
            return TextMessage(content=user_message, source="user")

        # Validate attachments first
        validation_errors = self.file_processor.validate_attachments(attachments)
        if validation_errors:
            error_msg = "Attachment validation failed: " + "; ".join(validation_errors)
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # Create multimodal message with attachments
        try:
            multi_modal_message = await self.file_processor.create_multimodal_message(
                user_message, attachments, source="user"
            )

            attachment_summary = self.file_processor.get_attachment_summary(attachments)

            self.logger.info(f"Created multimodal message with {attachment_summary}")

            return multi_modal_message

        except Exception as e:
            self.logger.error(f"Failed to create multimodal message: {e}")
            raise ValueError(f"Failed to process attachments: {e}")

    async def process_chat(
        self,
        session_id: str,
        user_message: Union[str, Dict, List],
        agent: AssistantAgent,
        run_id: Optional[str] = None,
        cancellation_token: Optional[CancellationToken] = None,
        attachments: Optional[List[MessageAttachment]] = None,
    ) -> AsyncGenerator[Dict, None]:
        """
        Process chat with optimized streaming and error handling
        """
        self.logger.info(f"Starting chat processing for session: {session_id}")

        messages_to_send = []
        processed_message = None

        async with self._session_context(session_id) as session:
            try:
                # Get session identifiers for memory operations
                user_id, agent_id = await self._get_session_identifiers(session_id)

                # Update session with user message
                await self._update_session_memory(session_id, "user", user_message)

                if isinstance(user_message, str):
                    processed_message = user_message

                    self.logger.info(f"💾 STARTING MEMORY OPERATIONS for session: {session_id}")

                    # Store user query in memory
                    self.logger.info(f"📝 Storing user query in memory...")
                    await self.memory_service.store_user_query(
                        user_message=processed_message,
                        user_id=user_id,
                        agent_id=agent_id,
                        session_id=session_id,
                        metadata={"run_id": run_id}
                    )

                    # Get relevant context from memory
                    self.logger.info(f"🔍 Retrieving relevant context from memory...")
                    relevant_context = await self.memory_service.get_relevant_context(
                        query=processed_message,
                        user_id=user_id,
                        agent_id=agent_id,
                        session_id=session_id,
                        k=5,
                        max_context_length=1500,
                        min_relevance_score=0.70  # Balanced threshold for relevance
                    )

                    # If we have relevant context, prepend it to the message
                    if relevant_context:
                        enhanced_message = f"{relevant_context}\n\nCurrent query: {processed_message}"
                        processed_message = enhanced_message
                        self.logger.info(f"✅ Enhanced message with {len(relevant_context)} chars of context")
                    else:
                        self.logger.info(f"ℹ️  No relevant context found, proceeding with original message")

                # Create the main user message based on type and content
                if isinstance(user_message, dict):
                    # Handle dictionary input (structured content)
                    if "content" in user_message:
                        try:

                            class DynamicContent(BaseModel):
                                content: str = user_message.get("content", "")
                                data: Dict = user_message

                            structured_content = DynamicContent(
                                content=user_message.get("content", ""),
                                data=user_message,
                            )

                            main_message = StructuredMessage(
                                content=structured_content, source="user"
                            )
                        except Exception as e:
                            self.logger.warning(
                                f"Failed to create structured message from dict: {e}"
                            )
                            # Fallback to text message
                            main_message = TextMessage(
                                content=str(
                                    user_message.get(
                                        "content", json.dumps(user_message)
                                    )
                                ),
                                source="user",
                            )
                    else:
                        # Convert dict to text message
                        content = user_message.get("content", json.dumps(user_message))
                        main_message = TextMessage(content=content, source="user")

                    messages_to_send.append(main_message)

                elif isinstance(user_message, list):
                    # Handle list input (multiple content items)
                    for content in user_message:
                        if isinstance(content, str):
                            message = TextMessage(content=content, source="user")
                            messages_to_send.append(message)
                        if isinstance(content, dict):
                            message = TextMessage(
                                content=json.dumps(content), source="user"
                            )
                            messages_to_send.append(message)

                else:
                    # Handle string input (most common case)
                    main_message = await self._create_message_from_content(
                        str(processed_message), attachments
                    )
                    messages_to_send.append(main_message)

                print("messages_to_send", messages_to_send)

                if not hasattr(agent, "run_stream"):
                    raise TypeError(
                        f"Team object lacks run_stream method: {type(agent)}"
                    )

                # Process streaming responses
                chunk_id = 0
                complete_response_content = ""

                async for response in agent.run_stream(
                    task=messages_to_send,
                    cancellation_token=cancellation_token,
                    output_task_messages=False,
                ):
                    session.update_activity()

                    # Process response
                    agent_response = await self._process_message_response(
                        response, agent
                    )

                    if agent_response and agent_response.message_type != "unknown":

                        if agent_response.message_type == "text":
                            # Update session memory
                            await self._update_session_memory(
                                session_id,
                                agent_response.source,
                                agent_response.content,
                            )

                            # Accumulate response content for memory storage
                            complete_response_content += agent_response.content

                        # Yield response
                        yield {
                            "run_id": run_id,
                            "session_id": session_id,
                            "agent_response": agent_response.to_dict(),
                            "success": True,
                            "final": False,
                            "stream_chunk_id": chunk_id,
                        }
                        chunk_id += 1

                    if agent_response.message_type == "task_result":
                        break

                # Store complete agent response in memory
                if complete_response_content and isinstance(user_message, str):
                    self.logger.info(f"💾 Storing complete agent response in memory...")
                    self.logger.info(f"🤖 Agent response length: {len(complete_response_content)} chars")
                    await self.memory_service.store_agent_response(
                        agent_response=complete_response_content,
                        user_id=user_id,
                        agent_id=agent_id,
                        session_id=session_id,
                        metadata={"run_id": run_id}
                    )
                    self.logger.info(f"✅ Agent response stored in memory successfully")

                # Final response
                yield {
                    "run_id": run_id,
                    "session_id": session_id,
                    "agent_response": {
                        "content": "",
                        "message_type": "unknown",
                        "source": "unknown",
                        "models_usage": {"prompt_tokens": 0, "completion_tokens": 0},
                        "metadata": None,
                    },
                    "success": True,
                    "final": True,
                    "stream_chunk_id": chunk_id,
                }

            except Exception as e:
                self.logger.error(
                    f"Chat processing error for session {session_id}: {e}"
                )
                yield {
                    "run_id": run_id,
                    "session_id": session_id,
                    "agent_response": {"content": f"Error: {str(e)}"},
                    "success": False,
                    "final": True,
                    "stream_chunk_id": 0,
                    "error": str(e),
                }

    async def chat_with_agent_once(
        self,
        session_id: str,
        user_message: str,
        agent: AssistantAgent,
        run_id: Optional[str] = None,
        cancellation_token=None,
        attachments: Optional[List[MessageAttachment]] = None,
    ) -> Dict:
        """
        Process single chat interaction with improved error handling
        """
        self.logger.info(f"Starting single-agent chat for session: {session_id}")

        async with self._session_context(session_id):
            try:
                # Get session identifiers for memory operations
                user_id, agent_id = await self._get_session_identifiers(session_id)

                self.logger.info(f"💾 STARTING MEMORY OPERATIONS for single chat session: {session_id}")

                # Store user query in memory
                self.logger.info(f"📝 Storing user query in memory...")
                await self.memory_service.store_user_query(
                    user_message=user_message,
                    user_id=user_id,
                    agent_id=agent_id,
                    session_id=session_id,
                    metadata={"run_id": run_id}
                )

                # Get relevant context from memory
                self.logger.info(f"🔍 Retrieving relevant context from memory...")
                relevant_context = await self.memory_service.get_relevant_context(
                    query=user_message,
                    user_id=user_id,
                    agent_id=agent_id,
                    session_id=session_id,
                    k=5,
                    max_context_length=1500,
                    min_relevance_score=0.70  # Balanced threshold for relevance
                )

                # Enhance user message with context if available
                enhanced_message = user_message
                if relevant_context:
                    enhanced_message = f"{relevant_context}\n\nCurrent query: {user_message}"
                    self.logger.info(f"✅ Enhanced message with {len(relevant_context)} chars of context")
                else:
                    self.logger.info(f"ℹ️  No relevant context found, proceeding with original message")

                # Patch user proxy if needed
                if isinstance(agent, UserProxyAgent):
                    agent.input = self._create_user_input_patch(enhanced_message)

                # Update session memory
                await self._update_session_memory(session_id, "user", user_message)

                # Create appropriate message type (text or multimodal)
                message = await self._create_message_from_content(
                    enhanced_message, attachments
                )

                response = await agent.on_messages(
                    [message], cancellation_token=cancellation_token
                )

                # Create agent response
                agent_response = AgentResponse(
                    content=response.chat_message.content,
                    source=response.chat_message.source,
                    models_usage=self.message_processor.extract_models_usage(
                        response.chat_message
                    ),
                    message_type=getattr(response.chat_message, "type", "text"),
                    metadata=getattr(response.chat_message, "metadata", None),
                )

                # Update session memory
                await self._update_session_memory(
                    session_id, "assistant", agent_response.to_dict()
                )

                # Store agent response in memory
                self.logger.info(f"💾 Storing agent response in memory...")
                self.logger.info(f"🤖 Agent response length: {len(agent_response.content)} chars")
                await self.memory_service.store_agent_response(
                    agent_response=agent_response.content,
                    user_id=user_id,
                    agent_id=agent_id,
                    session_id=session_id,
                    metadata={"run_id": run_id}
                )
                self.logger.info(f"✅ Agent response stored in memory successfully")

                return {
                    "run_id": run_id,
                    "session_id": session_id,
                    "agent_response": agent_response.to_dict(),
                    "success": True,
                    "final": True,
                }

            except Exception as e:
                self.logger.error(
                    f"Single-agent chat error for session {session_id}: {e}"
                )
                return {
                    "run_id": run_id,
                    "session_id": session_id,
                    "agent_response": {"content": f"Error: {str(e)}"},
                    "success": False,
                    "final": True,
                    "error": str(e),
                }

    async def chat_with_direct_agent(
        self,
        agent: AssistantAgent,
        user_message: Union[str, Dict, List],
        run_id: Optional[str] = None,
        attachments: Optional[List[MessageAttachment]] = None,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
    ) -> Dict:
        """
        Enhanced direct agent chat supporting all AutoGen message types

        Args:
            agent: The AutoGen agent to chat with
            user_message: User message content (str, dict, or list for complex)
            run_id: Optional run identifier
            attachments: Optional file attachments
            user_id: Optional user identifier for memory operations
            agent_id: Optional agent identifier for memory operations

        Returns:
            Comprehensive response with all message type support
        """
        self.logger.info(f"Starting enhanced direct agent chat for run: {run_id}")

        # Handle different input message types
        messages_to_send = []
        processed_message = None

        try:
            # Extract user_id and agent_id from message if not provided
            if not user_id:
                user_id = await self.memory_service.extract_user_id_from_context(user_message)
            if not agent_id:
                agent_id = await self.memory_service.extract_agent_id_from_context(user_message)
                if not agent_id and hasattr(agent, 'id') and agent.id:
                    agent_id = agent.id
                elif not agent_id and hasattr(agent, 'name'):
                    agent_id = agent.name

            if isinstance(user_message, str):
                processed_message = user_message

                self.logger.info(f"💾 STARTING MEMORY OPERATIONS for direct chat, run_id: {run_id}")

                # Store user query in memory
                self.logger.info(f"📝 Storing user query in memory (direct chat)...")
                await self.memory_service.store_user_query(
                    user_message=processed_message,
                    user_id=user_id,
                    agent_id=agent_id,
                    conversation_id=run_id,
                    metadata={"run_id": run_id, "direct_chat": True}
                )

                # Get relevant context from memory
                self.logger.info(f"🔍 Retrieving relevant context from memory (direct chat)...")
                relevant_context = await self.memory_service.get_relevant_context(
                    query=processed_message,
                    user_id=user_id,
                    agent_id=agent_id,
                    conversation_id=run_id,
                    k=3,
                    max_context_length=1000,
                    min_relevance_score=0.70  # Balanced threshold for relevance
                )

                # Enhance message with context if available
                if relevant_context:
                    processed_message = f"{relevant_context}\n\nCurrent query: {processed_message}"
                    self.logger.info(f"✅ Enhanced direct chat message with {len(relevant_context)} chars of context")
                else:
                    self.logger.info(f"ℹ️  No relevant context found for direct chat, proceeding with original message")

            # Create the main user message based on type and content
            if isinstance(user_message, dict):
                # Handle dictionary input (structured content)
                if "content" in user_message:
                    try:

                        class DynamicContent(BaseModel):
                            content: str = user_message.get("content", "")
                            data: Dict = user_message

                        structured_content = DynamicContent(
                            content=user_message.get("content", ""),
                            data=user_message,
                        )

                        main_message = StructuredMessage(
                            content=structured_content, source="user"
                        )
                    except Exception as e:
                        self.logger.warning(
                            f"Failed to create structured message from dict: {e}"
                        )
                        # Fallback to text message
                        main_message = TextMessage(
                            content=str(
                                user_message.get("content", json.dumps(user_message))
                            ),
                            source="user",
                        )
                else:
                    # Convert dict to text message
                    content = user_message.get("content", json.dumps(user_message))
                    main_message = TextMessage(content=content, source="user")

                messages_to_send.append(main_message)

            elif isinstance(user_message, list):
                # Handle list input (multiple content items)
                for content in user_message:
                    if isinstance(content, str):
                        message = TextMessage(content=content, source="user")
                        messages_to_send.append(message)
                    if isinstance(content, dict):
                        message = TextMessage(
                            content=json.dumps(content), source="user"
                        )
                        messages_to_send.append(message)

            else:
                # Handle string input (most common case)
                main_message = await self._create_message_from_content(
                    str(processed_message), attachments
                )
                messages_to_send.append(main_message)

            # Send messages to agent and get response
            response = await agent.on_messages(
                messages_to_send, cancellation_token=CancellationToken()
            )

            agent_response = AgentResponse(
                content=response.chat_message.content,
                source=response.chat_message.source,
                models_usage=self.message_processor.extract_models_usage(
                    response.chat_message
                ),
                message_type=getattr(response.chat_message, "type", "text"),
                metadata=getattr(response.chat_message, "metadata", None),
            )

            # Store agent response in memory
            if isinstance(user_message, str):
                self.logger.info(f"💾 Storing agent response in memory (direct chat)...")
                self.logger.info(f"🤖 Agent response length: {len(agent_response.content)} chars")
                await self.memory_service.store_agent_response(
                    agent_response=agent_response.content,
                    user_id=user_id,
                    agent_id=agent_id,
                    conversation_id=run_id,
                    metadata={"run_id": run_id, "direct_chat": True}
                )
                self.logger.info(f"✅ Agent response stored in memory successfully (direct chat)")

            return {
                "run_id": run_id,
                "session_id": run_id,
                "agent_response": agent_response.to_dict(),
                "success": True,
                "final": True,
            }

        except Exception as e:
            self.logger.error(f"Direct agent chat error for run {run_id}: {e}")
            return {
                "run_id": run_id,
                "session_id": run_id,
                "agent_response": {},
                "error": str(e),
                "success": False,
                "final": True,
            }

    async def process_chat_stream(
        self, session_id: str, user_message: str
    ) -> AsyncGenerator[Dict, None]:
        """
        Optimized chat stream processing
        """
        try:
            # Get session data
            session_data = await self.session_manager.get_session_data(session_id)
            (
                agent_config,
                communication_type,
                memory,
                cancellation_token,
                organization_id,
                use_knowledge,
                variables,
                user_id,
            ) = session_data

            # Initialize agent and team
            agent = await self.agent_factory.initialize_chat_session(
                run_id=session_id,
                agent_config=agent_config,
                session_memory=memory,
                organization_id=organization_id,
                use_knowledge=use_knowledge,
                variables=variables,
            )

            # Process and yield responses
            async for response in self.process_chat(
                session_id=session_id,
                user_message=user_message,
                agent=agent,
            ):
                if response.get("success", False) and not response.get("final", False):
                    yield response.get("agent_response", {"content": ""})

        except Exception as e:
            self.logger.error(
                f"Chat stream processing error for session {session_id}: {e}"
            )
            yield {"content": f"Error: {str(e)}"}

    async def end_chat_session(self, session_id: str) -> bool:
        """End chat session with improved cleanup"""
        try:
            async with self._session_lock:
                self._active_sessions.pop(session_id, None)

            await self.session_manager.delete_session(session_id)
            self.logger.info(f"Session {session_id} ended successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error ending session {session_id}: {e}")
            return False

    async def get_session_history(self, session_id: str) -> List[Dict]:
        """Get session history with error handling"""
        try:
            return await self.session_manager.get_session_history(session_id)
        except Exception as e:
            self.logger.error(f"Error getting session history for {session_id}: {e}")
            return []

    async def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs"""
        async with self._session_lock:
            return list(self._active_sessions.keys())

    async def force_cleanup_session(self, session_id: str):
        """Force cleanup of a session"""
        async with self._session_lock:
            self._active_sessions.pop(session_id, None)
        self.logger.info(f"Force cleaned up session: {session_id}")

    async def shutdown(self):
        """Graceful shutdown"""
        if hasattr(self, "_cleanup_task"):
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

        # Clean up all active sessions
        async with self._session_lock:
            self._active_sessions.clear()

        self.logger.info("ChatProcessor shutdown complete")

    def __del__(self):
        """Cleanup on destruction"""
        if hasattr(self, "_cleanup_task") and not self._cleanup_task.done():
            self._cleanup_task.cancel()
