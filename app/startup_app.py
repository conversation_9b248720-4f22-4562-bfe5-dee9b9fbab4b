#!/usr/bin/env python3
"""
Application startup script with model preloading.

This script handles the initialization of models and services
before the main application starts.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from startup import preload_sentence_transformer
from shared.config.base import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)


def startup_models():
    """Preload all models at application startup."""
    logger.info("🚀 STARTING APPLICATION INITIALIZATION")
    logger.info("=" * 80)
    
    start_time = time.time()
    
    try:
        # Load configuration
        logger.info("📋 Loading application configuration...")
        settings = get_settings()
        logger.info(f"✅ Configuration loaded")
        logger.info(f"   🗄️  Qdrant: {settings.qdrant.host}:{settings.qdrant.port}")
        logger.info(f"   📦 Collection: {settings.qdrant.collection_name}")
        
        # Preload sentence transformer model
        logger.info("\n🤖 PRELOADING SENTENCE TRANSFORMER MODEL")
        logger.info("-" * 60)
        model = preload_sentence_transformer()
        
        # Model information
        dimensions = model.get_sentence_embedding_dimension()
        device = model.device
        max_seq_length = getattr(model, 'max_seq_length', 'Unknown')
        
        logger.info("\n📊 MODEL READY FOR PRODUCTION")
        logger.info("-" * 60)
        logger.info(f"   📦 Model: all-MiniLM-L6-v2")
        logger.info(f"   📏 Dimensions: {dimensions}")
        logger.info(f"   🏠 Device: {device}")
        logger.info(f"   📝 Max Sequence Length: {max_seq_length}")
        logger.info(f"   🚀 Status: LOADED AND CACHED")
        
        total_time = time.time() - start_time
        logger.info(f"\n✅ APPLICATION INITIALIZATION COMPLETE")
        logger.info(f"   ⏱️  Total startup time: {total_time:.2f} seconds")
        logger.info("=" * 80)
        
        return {
            "model": model,
            "dimensions": dimensions,
            "device": str(device),
            "startup_time": total_time
        }
        
    except Exception as e:
        logger.error(f"❌ STARTUP FAILED: {e}")
        import traceback
        traceback.print_exc()
        raise


def main():
    """Main startup function."""
    try:
        startup_info = startup_models()
        
        logger.info("\n🎯 STARTUP SUMMARY")
        logger.info("=" * 80)
        logger.info("✅ All models preloaded successfully")
        logger.info("✅ Application ready to serve requests")
        logger.info("✅ Memory operations will be fast (no model loading delays)")
        logger.info("✅ Embeddings will be generated locally")
        logger.info(f"✅ Vector dimensions: {startup_info['dimensions']}")
        logger.info(f"✅ Startup time: {startup_info['startup_time']:.2f}s")
        logger.info("=" * 80)
        
        return startup_info
        
    except Exception as e:
        logger.error(f"❌ Application startup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
